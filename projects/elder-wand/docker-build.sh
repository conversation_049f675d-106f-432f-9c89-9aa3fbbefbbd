#!/bin/bash

# =============================================================================
# Docker Build Script for Elder Wand Application
# =============================================================================
# 
# This script automates the Docker build process with:
# - Multi-stage builds
# - Proper tagging
# - Optional testing
# - Registry support
# =============================================================================

# =============================================================================
# Script Configuration
# =============================================================================
set -e  # Exit on any error

# =============================================================================
# Color Definitions
# =============================================================================
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# Build Configuration
# =============================================================================
IMAGE_NAME="elder-wand"
REGISTRY="your-registry.com"  # Change this to your registry
VERSION=${1:-latest}
BUILD_CONTEXT="../../"
DOCKERFILE="Dockerfile"

# =============================================================================
# Helper Functions
# =============================================================================
print_header() {
    echo -e "${BLUE}=============================================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}=============================================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# =============================================================================
# Main Build Process
# =============================================================================
print_header "Building Elder Wand Docker Image"

# Validate inputs
if [ -z "$VERSION" ]; then
    print_error "Version is required"
    echo "Usage: $0 <version> [--test]"
    echo "Example: $0 v1.0.0 --test"
    exit 1
fi

# =============================================================================
# Build Image
# =============================================================================
print_warning "Building image: ${IMAGE_NAME}:${VERSION}"

docker build \
    --file ${DOCKERFILE} \
    --target production \
    --tag ${IMAGE_NAME}:${VERSION} \
    --tag ${IMAGE_NAME}:latest \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    ${BUILD_CONTEXT}

print_success "Docker build completed"

# =============================================================================
# Registry Tagging (Optional)
# =============================================================================
if [ ! -z "$REGISTRY" ] && [ "$REGISTRY" != "your-registry.com" ]; then
    print_warning "Tagging for registry: ${REGISTRY}/${IMAGE_NAME}:${VERSION}"
    docker tag ${IMAGE_NAME}:${VERSION} ${REGISTRY}/${IMAGE_NAME}:${VERSION}
    docker tag ${IMAGE_NAME}:latest ${REGISTRY}/${IMAGE_NAME}:latest
    print_success "Registry tagging completed"
fi

# =============================================================================
# Display Results
# =============================================================================
print_success "Build completed successfully!"
print_warning "Images created:"
docker images | grep ${IMAGE_NAME}

# =============================================================================
# Optional Testing
# =============================================================================
if [ "$2" = "--test" ]; then
    print_header "Running Container Tests"
    
    # Start test container
    print_warning "Starting test container..."
    docker run --rm -d --name test-elder-wand -p 8080:8080 ${IMAGE_NAME}:${VERSION}
    
    # Wait for container to start
    print_warning "Waiting for container to start..."
    sleep 10
    
    # Test health endpoint
    print_warning "Testing health endpoint..."
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        print_success "Health check passed!"
    else
        print_error "Health check failed!"
        print_warning "Container logs:"
        docker logs test-elder-wand
        docker stop test-elder-wand
        exit 1
    fi
    
    # Cleanup test container
    print_warning "Cleaning up test container..."
    docker stop test-elder-wand
    print_success "Container tests passed!"
fi

# =============================================================================
# Build Summary
# =============================================================================
print_header "Build Summary"
print_success "Image: ${IMAGE_NAME}:${VERSION}"
print_success "Size: $(docker images ${IMAGE_NAME}:${VERSION} --format 'table {{.Size}}' | tail -1)"
print_success "Ready for deployment!" 