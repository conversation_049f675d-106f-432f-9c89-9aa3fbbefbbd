# =============================================================================
# Docker Compose Configuration for Elder Wand Application
# =============================================================================
# 
# This file defines the services for running the Elder Wand application
# in both production and development environments.
# =============================================================================

version: '3.8'

# =============================================================================
# Services Configuration
# =============================================================================
services:
  # =====================================================================
  # Production Service
  # =====================================================================
  elder-wand:
    build:
      context: ../..
      dockerfile: projects/elder-wand/Dockerfile
      target: production
    container_name: elder-wand-app
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - elder-wand-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.elder-wand.rule=Host(`elder-wand.localhost`)"
      - "traefik.http.services.elder-wand.loadbalancer.server.port=8080"

  # =====================================================================
  # Development Service (Optional)
  # =====================================================================
  elder-wand-dev:
    build:
      context: ../..
      dockerfile: projects/elder-wand/Dockerfile.dev
      target: development
    container_name: elder-wand-dev
    ports:
      - "4200:4200"
    environment:
      - NODE_ENV=development
    volumes:
      - ../../:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - elder-wand-network
    profiles:
      - dev

# =============================================================================
# Networks Configuration
# =============================================================================
networks:
  elder-wand-network:
    driver: bridge 