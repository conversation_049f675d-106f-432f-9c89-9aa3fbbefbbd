# =============================================================================
# Development Dockerfile for Elder Wand Angular Application
# =============================================================================
# 
# This Dockerfile creates a development environment with:
# - Hot reloading support
# - Volume mounting for live code changes
# - Development server on port 4200
# =============================================================================

FROM node:20-alpine AS development

# =============================================================================
# Set Working Directory
# =============================================================================
WORKDIR /app

# =============================================================================
# Install Dependencies
# =============================================================================
# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# =============================================================================
# Create Non-Root User
# =============================================================================
# Create user and group for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S angular -u 1001

# =============================================================================
# Install Node Dependencies
# =============================================================================
# Copy package files and install dependencies
COPY package*.json ./
RUN npm ci --only=production=false --silent

# =============================================================================
# Copy Source Code
# =============================================================================
# Copy all source code (will be overridden by volume mount in development)
COPY . .

# =============================================================================
# Set Permissions
# =============================================================================
# Change ownership to non-root user
RUN chown -R angular:nodejs /app

# =============================================================================
# Switch to Non-Root User
# =============================================================================
USER angular

# =============================================================================
# Expose Port
# =============================================================================
EXPOSE 4200

# =============================================================================
# Health Check
# =============================================================================
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:4200/ || exit 1

# =============================================================================
# Start Development Server
# =============================================================================
# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start Angular development server
CMD ["npm", "run", "start:elder-wand"] 