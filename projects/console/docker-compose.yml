# =============================================================================
# Docker Compose Configuration for Console Application
# =============================================================================
# 
# This file defines the services for running the Console application
# in both production and development environments.
# =============================================================================

version: '3.8'

# =============================================================================
# Services Configuration
# =============================================================================
services:
  # =====================================================================
  # Production Service
  # =====================================================================
  console:
    build:
      context: ../..
      dockerfile: projects/console/Dockerfile
      target: production
    container_name: console-app
    ports:
      - "8083:8080"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - console-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.console.rule=Host(`console.localhost`)"
      - "traefik.http.services.console.loadbalancer.server.port=8080"

  # =====================================================================
  # Development Service (Optional)
  # =====================================================================
  console-dev:
    build:
      context: ../..
      dockerfile: projects/console/Dockerfile.dev
      target: development
    container_name: console-dev
    ports:
      - "4200:4200"
    environment:
      - NODE_ENV=development
    volumes:
      - ../../:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - console-network
    profiles:
      - dev

# =============================================================================
# Networks Configuration
# =============================================================================
networks:
  console-network:
    driver: bridge 