# =============================================================================
# Multi-stage Dockerfile for Console Angular Application
# =============================================================================
# 
# This Dockerfile uses a multi-stage build approach:
# 1. Builder stage: Compiles the Angular application
# 2. Production stage: Serves the built application with Nginx
# =============================================================================

# =============================================================================
# STAGE 1: BUILDER
# =============================================================================
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# =============================================================================
# Install Dependencies
# =============================================================================
# Copy package files first for better layer caching
COPY package*.json ./
COPY angular.json ./
COPY tsconfig.json ./
COPY staticwebapp.config.json ./
COPY .npmrc ./
COPY play-plus.tgz ./

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# =============================================================================
# Copy Source Code
# =============================================================================
# Copy application source and shared libraries
COPY projects/console ./projects/console
COPY projects/shared ./projects/shared

# =============================================================================
# Build Application
# =============================================================================
RUN npm run build:console

# =============================================================================
# STAGE 2: PRODUCTION
# =============================================================================
FROM nginx:alpine AS production

# =============================================================================
# Install Dependencies
# =============================================================================
# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# =============================================================================
# Create Non-Root User
# =============================================================================
# Create user and group for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S angular -u 1001

# =============================================================================
# Configure Nginx
# =============================================================================
# Copy custom nginx configuration
COPY projects/console/nginx.conf /etc/nginx/nginx.conf

# =============================================================================
# Copy Built Application
# =============================================================================
# Copy built application from builder stage
COPY --from=builder --chown=angular:nodejs /app/dist/console /usr/share/nginx/html

# =============================================================================
# Set Permissions
# =============================================================================
# Create necessary directories and set proper ownership
RUN mkdir -p /var/cache/nginx /var/log/nginx /tmp/nginx /tmp && \
    chown -R angular:nodejs /var/cache/nginx /var/log/nginx /tmp/nginx /tmp /etc/nginx/conf.d

# =============================================================================
# Switch to Non-Root User
# =============================================================================
USER angular

# =============================================================================
# Expose Port
# =============================================================================
EXPOSE 8080

# =============================================================================
# Health Check
# =============================================================================
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# =============================================================================
# Start Application
# =============================================================================
# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start nginx in foreground
CMD ["nginx", "-g", "daemon off;"] 