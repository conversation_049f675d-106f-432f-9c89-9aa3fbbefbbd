import { Injectable } from '@angular/core';
import { FormControl } from '@angular/forms';
import { BehaviorSubject, Observable } from 'rxjs';
import { SelectOption } from '../components/select-dropdown/select-dropdown.component';

export interface MetadataValues {
  org: string;
  domain: string;
  project: string;
  team: string;
}

export interface MetadataDropdownConfig {
  enabled: boolean;
  label: string;
  placeholder: string;
  showStatus: boolean;
  statusText: {
    saved: string;
    notSaved: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class MetadataConfigService {
  private metadataValues$ = new BehaviorSubject<MetadataValues>({
    org: '',
    domain: '',
    project: '',
    team: ''
  });

  // Form controls
  orgControl = new FormControl('');
  domainControl = new FormControl('');
  projectControl = new FormControl('');
  teamControl = new FormControl('');

  // Options
  orgOptions: SelectOption[] = [
    { value: 'ascendion', label: 'Ascendion' },
    { value: 'company2', label: 'Company 2' },
    { value: 'company3', label: 'Company 3' }
  ];
  domainOptions: SelectOption[] = [];
  projectOptions: SelectOption[] = [];
  teamOptions: SelectOption[] = [];

  constructor() {}

  getMetadataValues(): Observable<MetadataValues> {
    return this.metadataValues$.asObservable();
  }

  getCurrentValues(): MetadataValues {
    return this.metadataValues$.value;
  }

  updateMetadataValues(values: Partial<MetadataValues>): void {
    const currentValues = this.metadataValues$.value;
    const newValues = { ...currentValues, ...values };
    this.metadataValues$.next(newValues);
  }

  // API calls for cascading dropdowns
  loadDomainOptions(org: string): void {
    // Simulate API call - replace with actual service call
    const domainOptionsMap: {[key: string]: SelectOption[]} = {
      'ascendion': [
        { value: 'engineering', label: 'Engineering' },
        { value: 'marketing', label: 'Marketing' },
        { value: 'sales', label: 'Sales' }
      ],
      'company2': [
        { value: 'tech', label: 'Technology' },
        { value: 'operations', label: 'Operations' }
      ],
      'company3': [
        { value: 'research', label: 'Research' },
        { value: 'development', label: 'Development' }
      ]
    };
    
    this.domainOptions = domainOptionsMap[org] || [];
  }

  loadProjectOptions(org: string, domain: string): void {
    // Simulate API call - replace with actual service call
    const projectOptions: SelectOption[] = [
      { value: 'project1', label: 'Project Alpha' },
      { value: 'project2', label: 'Project Beta' },
      { value: 'project3', label: 'Project Gamma' }
    ];
    
    this.projectOptions = projectOptions;
  }

  loadTeamOptions(org: string, domain: string, project: string): void {
    // Simulate API call - replace with actual service call
    const teamOptions: SelectOption[] = [
      { value: 'team1', label: 'Team Alpha' },
      { value: 'team2', label: 'Team Beta' },
      { value: 'team3', label: 'Team Gamma' }
    ];
    
    this.teamOptions = teamOptions;
  }

  resetForm(): void {
    this.orgControl.setValue('');
    this.domainControl.setValue('');
    this.projectControl.setValue('');
    this.teamControl.setValue('');
    this.domainOptions = [];
    this.projectOptions = [];
    this.teamOptions = [];
  }

  applyValues(): MetadataValues {
    const values: MetadataValues = {
      org: this.orgControl.value || '',
      domain: this.domainControl.value || '',
      project: this.projectControl.value || '',
      team: this.teamControl.value || ''
    };
    
    this.updateMetadataValues(values);
    return values;
  }

  hasValues(): boolean {
    const values = this.getCurrentValues();
    return !!(values.org || values.domain || values.project || values.team);
  }
}
