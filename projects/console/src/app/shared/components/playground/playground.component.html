<div class="playground-container">
  <div class="button-container">
    <div class="dropdown-container">
      <app-select-dropdown [options]="promptOptions" placeholder="Choose Prompt" [isMultiSelect]="false"
        (selectionChange)="onPromptChange($event)"></app-select-dropdown>
    </div>

    <div class="btn-menu">
      <!-- <button class="action-btn">Send for Approval</button> -->
      <ava-button
        label="Send for Approval"
        size="small"
        state="active"
        variant="primary"
        gradient="linear-gradient(45deg,lightblue,darkblue)"
      ></ava-button>
      <div class="menu-icon" (click)="toggleMenu()" #menuIcon>
        <span></span>
        <span></span>
        <span></span>
      </div>

      <div class="dot-dropdown-menu" *ngIf="isMenuOpen">
        <button (click)="save()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17 3H5a2 2 0 0 0-2 2v14l4-4h10a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z" />
          </svg>
          Save
        </button>

        <button (click)="export()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
            <path d="M5 20h14v-2H5m14-9h-4V3H9v6H5l7 7 7-7z" />
          </svg>
          Export
        </button>
      </div>
    </div>
  </div>
  <div class="layout" #messagesContainer>
    <!-- Messages in normal order -->
    <div *ngFor="let msg of messages; trackBy: trackByIndex" class="message-row" [ngClass]="msg.from">
      <div [ngClass]="{
          'bot-response': msg.from === 'ai',
          'user-message': msg.from === 'user',
        }">
        {{ msg.text }}
      </div>
    </div>

    <!-- Animated loading indicator when API call is in progress -->
    <div *ngIf="isLoading && showLoader" class="message-row ai">
      <div class="bot-response loading-message">
        <div class="typing-indicator">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </div>
    </div>

    <!-- Simple text loading indicator for tool testing -->
    <div *ngIf="isLoading && !showLoader" class="message-row ai">
      <div class="bot-response">
        ...
      </div>
    </div>
  </div>

  <div class="input-container">
    <button class="attach-btn" title="Attach File">
      <svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='currentColor' class='bi bi-paperclip'
        viewBox='0 0 16 16'>
        <path
          d='M4.5 3a2.5 2.5 0 0 1 5 0v9a1.5 1.5 0 0 1-3 0V5a.5.5 0 0 1 1 0v7a.5.5 0 0 0 1 0V3a1.5 1.5 0 1 0-3 0v9a2.5 2.5 0 0 0 5 0V5a.5.5 0 0 1 1 0v7a3.5 3.5 0 1 1-7 0z' />
      </svg>
    </button>

    <textarea [(ngModel)]="inputText" [disabled]="isDisabled || isLoading"
      (keydown.enter)="handleSendMessage(); $event.preventDefault()" placeholder="Enter requirement">
    </textarea>

    <div class="right-icons">
      <button class="edit-btn" title="Edit">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 24 24">
          <path
            d="M4 22l14-14-2-2-14 14v2h2zm17-20h-3v2h3v3h2V4c0-1.1-.9-2-2-2zM4 2H2v3h2V2zm0 7H2v3h2V9zm17 7h-3v2h3v3h2v-3c0-1.1-.9-2-2-2zm-7 4h-3v2h3v-2z" />
        </svg>
      </button>
      <button class="send-btn" title="Send" (click)="handleSendMessage()" [disabled]="isLoading || isDisabled">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path d="M4 20l16-8L4 4v6l10 2-10 2z" />
        </svg>
      </button>
    </div>
  </div>

  <div class="toggle-container">
    <ava-toggle
      [checked]="true"
      [title]="'AI Principles'"
      [position]="'left'"
      size="small"
      (checkedChange)="onAiPrincipleToggle($event)">
    </ava-toggle>
  </div>
</div>
