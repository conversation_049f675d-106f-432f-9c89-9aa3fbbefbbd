.playground-container {
  display: flex;
  flex-direction: column;
  font-family: "Inter", sans-serif;
  width: 100%;
  height: 650px; /* Fixed height for playground */
  max-height: 650px; /* Ensure it doesn't exceed this height */
  overflow: hidden; /* Prevent container overflow */
  @media (max-width: 1400px) {
      width: 60%;
    }
    
    @media (max-width: 1200px) {
      width: 60%;
    }
  
}

/* Top Button Container */
.button-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  height: 56px;
  min-height: 56px; /* Fixed height */
  flex-shrink: 0; /* Prevent shrinking */
  border-bottom: 1px solid #eee;
}

/* Dropdown Button */
.dropdown-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background: white;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  min-width: 100px;
}

.dropdown-btn .arrow-down {
  width: 10px;
  height: 6px;
  margin-left: 8px;
}

/* Action Button and Menu */
.btn-menu {
  display: flex;
  align-items: center;
  gap: 8px;
}

//.action-btn {
  //padding: 0.25rem 0.5rem;
  //background: lightblue; //replace this
  //color: white;
  //border: none;
  //border-radius: 6px;
  //cursor: pointer;
  //font-size: 14px;
//}


/* Three Dot Menu */
.menu-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 3px;
  cursor: pointer;
}

.menu-icon span {
  display: block;
  width: 3px;
  height: 3px;
  background: black;
  border-radius: 50%;
}

/* Layout (chat area) */
.layout {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1; /* fill available space */
  padding: 24px 16px;
  overflow-y: auto; /* Enable scrolling within the chat area */
  overflow-x: hidden; /* Hide horizontal scroll */
  background: #fff;
  align-items: stretch;
  justify-content: flex-start;
  min-height: 0; /* Allow flex item to shrink below content size */
  max-height: 100%; /* Ensure it doesn't exceed container */
}

/* Chat messages common style */
.message {
  max-width: 60%;
  font-size: 14px;
  border-radius: 8px;
  padding: 16px 24px;
}

.message-row {
  display: flex;
  width: 100%;
}

.message-row.ai {
  justify-content: flex-start;
}

.message-row.user {
  justify-content: flex-end;
}

/* User messages (left side, same as bot) */

.user-message {
  display: inline-flex; /* bubble fits to content */
  flex-direction: column;
  justify-content: center;
  align-items: flex-start; /* text left-aligned inside */
  align-self: flex-end; /* positions entire bubble right side */
  background: #c2c4cd;
  color: #333;
  border-radius: 8px;
  padding: 16px 24px;
  max-width: 60%;
  word-wrap: break-word;
  text-align: left;
  margin-bottom: 0.5rem;
}

.user-message:empty {
  background: none;
  padding: 0;
}

/* Bot messages (left side) */
.bot-response {
  align-self: flex-start;
  background: #f1f1f1; /* Neutrals-N-100 */
  color: #333;
  display: flex;
  padding: 16px 24px;
  border-radius: 8px;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  margin-bottom: 0.5rem;
}

/* Loading message styling */
.loading-message {
  min-height: 20px;
  display: flex;
  align-items: center;
}

/* Typing indicator with animated dots */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-indicator .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #666;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator .dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Result Label (optional) */
.result-label {
  display: flex;
  width: 186px;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: #f1f1f1;
  font-size: 14px;
  color: #333;
  margin-bottom: 0.5rem;
}

/* Input container at bottom */
.input-container {
  position: relative;
  display: flex;
  align-items: flex-end;
  border: 1px solid #ccc;
  border-radius: 12px;
  padding: 8px 12px;
  gap: 8px;
  background: white;
  flex-shrink: 0; /* Prevent input from shrinking */
  min-height: 60px; /* Fixed minimum height */
  max-height: 120px; /* Maximum height for textarea expansion */
}

.input-container button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-container button svg {
  fill: #333;
}

.input-container textarea {
  flex: 1;
  min-height: calc(1.2em * 5); /* ✅ ensures ~5 lines visible by default */
  max-height: calc(1.2em * 5); /* ✅ limits height to ~5 lines */
  line-height: 1.2em; /* consistent line height for calculation */
  padding: 12px 48px 12px 0px;
  font-size: 14px;
  border: none;
  resize: none; /* disable manual resizing if not needed */
  font-family: "Inter", sans-serif;
  outline: none;
  overflow-y: auto;

  &:disabled {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }
}

/* Right icons (pencil + send) */
.input-container .right-icons {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
}

.input-container .right-icons button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;

    svg {
      fill: #ccc;
    }
  }
}

.edit-btn {
  width: 24px;
  height: 24px;
  aspect-ratio: 1/1;
}
.input-container svg {
  fill: #333;
}
.right-icons {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Toggle container */
.toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  padding: 8px 16px;
  font-family: "Inter", sans-serif;
  flex-shrink: 0; /* Prevent toggle from shrinking */
  height: 40px; /* Fixed height */
  min-height: 40px; /* Fixed minimum height */
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 20px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.toggle-switch input:checked + .slider {
  background-color: #4caf50;
}

.toggle-switch input:checked + .slider:before {
  transform: translateX(20px);
}

.toggle-label {
  color: #333;
}

.dot-dropdown-menu {
  position: absolute;
  right: 12px;
  top: 120px;
  background: white;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  z-index: 10;
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.dot-dropdown-menu button:hover {
  background: #f0f0f0;
}

.dot-dropdown-menu button {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  width: 100%;
  line-height: 1.2;
}

.dot-dropdown-menu button svg {
  flex-shrink: 0;
  display: block;
  height: 16px;
  width: 16px;
  vertical-align: middle;
}

//list drop down
.dropdown-container {
  position: relative;
  display: inline-block;
}

.tool-dropdown-menu {
  position: absolute;
  top: 100%; /* ✅ below the button */
  left: 0;
  display: flex;
  flex-direction: column;
  background: white;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 140px;
}
