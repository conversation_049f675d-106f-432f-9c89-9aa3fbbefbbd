.canvas-board-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f8f9fa;
  background-image:
    radial-gradient(circle, #d1d5db 1px, transparent 1px);
  background-size: 20px 20px;
  border-radius: 8px;
  overflow: hidden;
}

// Built-in Header Inputs Section
.header-inputs-section {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;

  .header-inputs-container {
    display: flex;
    align-items: center;
    gap: 16px;

    .input-group {
      display: flex;
      align-items: center;
      gap: 8px;
      position: relative;

      &:first-child {
        .edit-icon {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: var(--text-secondary);
          pointer-events: none;
        }
      }

      .agent-name-input {
        min-width: 180px;
        padding: 8px 32px 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        color: #374151;
        background-color: white;
        transition: border-color 0.15s ease, box-shadow 0.15s ease;

        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        &::placeholder {
          color: #9ca3af;
        }
      }

      // Metadata dropdown specific styles
      &.metadata-dropdown-container {
        position: relative;

        .metadata-dropdown-trigger {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background-color: white;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          color: #374151;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.15s ease;
          min-width: 140px;

          &:hover {
            border-color: #3b82f6;
            background-color: #f9fafb;
          }

          svg {
            color: #6b7280;
          }
        }

        .metadata-status {
          font-size: 12px;
          color: #6b7280;
          margin-left: 8px;
        }

        .metadata-popover {
          position: absolute;
          top: 100%;
          left: 0;
          z-index: 1000;
          background: var(--card-bg);
          border: 1px solid var(--border-color);
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          padding: 20px;
          min-width: 320px;
          margin-top: 8px;

          .dropdown-row-vertical {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 20px;
          }

          .popover-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;

            .cancel-btn {
              padding: 8px 16px;
              background-color: transparent;
              border: 1px solid var(--border-color);
              border-radius: 6px;
              color: var(--text-primary);
              font-size: 14px;
              cursor: pointer;
              transition: all 0.15s ease;

              &:hover {
                background-color: var(--hover-bg);
                border-color: var(--border-hover);
              }
            }

            .apply-btn {
              padding: 8px 16px;
              background-color: var(--dashboard-primary);
              border: 1px solid var(--dashboard-primary);
              border-radius: 6px;
              color: white;
              font-size: 14px;
              cursor: pointer;
              transition: all 0.15s ease;

              &:hover {
                background-color: var(--dashboard-primary-hover);
                border-color: var(--dashboard-primary-hover);
              }
            }
          }
        }

        .metadata-backdrop {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 999;
          background: transparent;
        }
      }
    }
  }
}

// Custom Input Fields Section
.input-fields-section {
  padding: 16px;
  background-color: var(--dashboard-bg-lighter);
  border-bottom: 1px solid var(--form-input-border);

  .input-fields-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .input-field {
      display: flex;
      flex-direction: column;
      min-width: 200px;
      flex: 1;

      .field-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 6px;

        .required-asterisk {
          color: var(--error-color);
          margin-left: 2px;
        }
      }

      .field-input,
      .field-textarea {
        padding: 8px 12px;
        border: 1px solid var(--form-input-border);
        border-radius: 6px;
        font-size: 14px;
        color: var(--text-primary);
        background-color: var(--form-input-bg);
        transition: border-color 0.15s ease, box-shadow 0.15s ease;

        &:focus {
          outline: none;
          border-color: var(--dashboard-primary);
          box-shadow: 0 0 0 2px rgba(var(--dashboard-primary-rgb), 0.1);
        }

        &:disabled {
          background-color: var(--form-input-disabled-bg);
          color: var(--text-disabled);
          cursor: not-allowed;
        }

        &::placeholder {
          color: var(--text-placeholder);
        }
      }

      .field-textarea {
        resize: vertical;
        min-height: 80px;
      }
    }
  }
}

// Metadata Section
.metadata-section {
  padding: 16px;
  background-color: var(--dashboard-bg-lighter);
  border-bottom: 1px solid var(--form-input-border);

  .metadata-container {
    .metadata-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 16px;
    }

    .dropdown-row-vertical {
      display: flex;
      flex-direction: column;
      gap: 12px;

      @media (min-width: 768px) {
        flex-direction: row;
        flex-wrap: wrap;

        > * {
          flex: 1;
          min-width: 200px;
        }
      }
    }
  }
}

.canvas-tools-toolbar {
  position: absolute;
  display: flex;
  gap: 4px;
  z-index: 20; // Higher than input fields (z-index: 10)
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  user-select: none;
  align-items: center;
}

.tool-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  color: #6b7280;
  transition: none;

  &:hover {
    background-color: #e5e7eb;
    color: #374151;
  }

  &.active {
    background-color: #3b82f6;
    color: #ffffff;
    border-color: #3b82f6;
  }

  &.drag-handle {
    cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.floating-toolbar {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  align-items: center;
  z-index: 1000;
}

.toolbar-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  color: #6b7280;
  transition: none;

  &:hover {
    background-color: #e5e7eb;
    color: #374151;
  }

  &:active {
    background-color: #d1d5db;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.primary-btn {
  padding: 6px 16px;
  background: #000000;
  border: none;
  border-radius: 6px;
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: none;
  min-width: 80px;
  justify-content: center;
  height: 32px;

  &:hover {
    background: #1f2937;
  }

  &:active {
    background: #374151;
  }
}

.navigation-hint {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 11px;
  color: var(--text-color);

  span {
    display: flex;
    align-items: center;
    gap: 4px;

    kbd {
      background-color: var(--form-input-bg);
      border: 1px solid var(--form-input-border);
      border-radius: 3px;
      padding: 2px 6px;
      font-size: 10px;
      font-family: monospace;
      color: var(--text-secondary);
    }
  }
}

.canvas-container {
  flex-grow: 1;
  background-color: var(--dashboard-bg-lighter);
  position: relative;
  overflow: hidden;
  border: 1px dashed var(--form-input-border);
  cursor: grab;
  border-radius: 8px;
  transition: none;

  &.show-grid {
    background-image:
      linear-gradient(rgba(200, 200, 200, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(200, 200, 200, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  &:active {
    cursor: grabbing;
  }

  // Disabled state when mouse interactions are disabled
  &.disabled {
    cursor: not-allowed;
    opacity: 0.8;

    &::after {
      content: 'Canvas interactions are disabled';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 12px;
      pointer-events: none;
      z-index: 1001;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::after {
      opacity: 1;
    }
  }
}

// Viewport container that applies the transform
.canvas-viewport {
  width: 100%;
  height: 100%;
  position: absolute;
  transform-origin: 0 0;
  transition: none;
}

// Canvas content container
.canvas-content {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}

// Connections layer
.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  pointer-events: none;

  .edge-path {
    fill: none;
    stroke-width: 2px;
    pointer-events: all;
    cursor: pointer;
    stroke: var(--dashboard-primary);
    transition: none;

    &.animated {
      stroke-dasharray: 8 4;
      animation: dash 1.5s linear infinite;
    }

    &:hover {
      stroke-width: 3px;
      stroke: var(--dashboard-primary-hover);
    }
  }

  .arrow-head {
    fill: var(--dashboard-primary);
    stroke: none;
    opacity: 1;
  }

  // Ensure markers are visible
  marker {
    overflow: visible;
  }
}

// Nodes container
.nodes-container {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 10;
}

// Temporary connection layer
.temp-connection-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 15;
  pointer-events: none;

  .temp-connection-path {
    fill: none;
    stroke-width: 2px;
    stroke-dasharray: 8 4;
    opacity: 0.8;
    stroke: var(--dashboard-primary);
    animation: dash 1.5s linear infinite;
  }

  // Ensure temp connection arrows are visible
  marker {
    overflow: visible;
  }
}

// Fallback message
.no-nodes-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-secondary);
  font-size: 16px;
  text-align: center;
  pointer-events: none;
  z-index: 5;
}

// Animation for edge paths - sharper and smoother
@keyframes dash {
  from {
    stroke-dashoffset: 12;
  }
  to {
    stroke-dashoffset: 0;
  }
}

// Navigation hint styling
.navigation-hint {
  padding: 8px 16px;
  background-color: var(--dashboard-bg-lighter);
  border-bottom: 1px solid var(--form-input-border);
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;

  span {
    margin: 0 4px;
  }
}
