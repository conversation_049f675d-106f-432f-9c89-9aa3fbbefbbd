import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, OnDestroy, OnChanges, ChangeDetectorRef, ContentChild, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import { SelectDropdownComponent, SelectOption } from '../select-dropdown/select-dropdown.component';

export interface CanvasNode {
  id: string;
  type: string;
  data: any;
  position: { x: number; y: number };
}

export interface CanvasEdge {
  id: string;
  source: string;
  target: string;
  animated?: boolean;
}

export interface CustomInputField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'number' | 'email';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: SelectOption[]; // For select type
  control?: FormControl;
  value?: any;
  width?: string;
  height?: string;
}

export interface MetadataConfig {
  enabled: boolean;
  orgOptions: SelectOption[];
  domainOptions: SelectOption[];
  projectOptions: SelectOption[];
  teamOptions: SelectOption[];
  orgControl: FormControl;
  domainControl: FormControl;
  projectControl: FormControl;
  teamControl: FormControl;
}

interface TempConnection {
  isActive: boolean;
  sourceNodeId?: string;
  targetNodeId?: string;
  sourceHandleType?: 'source' | 'target';
  sourceX?: number;
  sourceY?: number;
  targetX?: number;
  targetY?: number;
}

type CanvasToolMode = 'select' | 'pan' | 'zoom' | 'disabled';

interface CanvasViewport {
  zoom: number;
  x: number;
  y: number;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
}

interface NodeConnectionPoints {
  [nodeId: string]: {
    top: { x: number; y: number };
    right: { x: number; y: number };
    bottom: { x: number; y: number };
    left: { x: number; y: number };
  };
}

@Component({
  selector: 'app-canvas-board',
  standalone: true,
  imports: [CommonModule, SelectDropdownComponent],
  templateUrl: './canvas-board.component.html',
  styleUrls: ['./canvas-board.component.scss']
})
export class CanvasBoardComponent implements AfterViewInit, OnDestroy, OnChanges {
  @ViewChild('canvasContainer') canvasContainer!: ElementRef;
  @ContentChild('nodeTemplate') nodeTemplate!: TemplateRef<any>;

  // Inputs
  @Input() nodes: CanvasNode[] = [];
  @Input() edges: CanvasEdge[] = [];
  @Input() showGrid: boolean = true;
  @Input() enablePan: boolean = false; // Disabled by default
  @Input() enableZoom: boolean = false; // Disabled by default
  @Input() enableConnections: boolean = true;
  @Input() minZoom: number = 0.1;
  @Input() maxZoom: number = 2;
  @Input() connectionColor: string = 'var(--dashboard-primary)';
  @Input() fallbackMessage: string = 'Drop items here to get started';
  @Input() navigationHints: string[] = [
    'Select toolbar options to enable canvas interactions',
    'Alt + Drag to pan canvas (when enabled)',
    'Mouse wheel to zoom (when enabled)',
    'Space to reset view'
  ];
  @Input() showToolbar: boolean = true;
  @Input() primaryButtonText: string = 'Execute';
  @Input() primaryButtonIcon: string = '';
  @Input() enableUndo: boolean = true;
  @Input() enableRedo: boolean = true;
  @Input() enableReset: boolean = true;
  @Input() showCanvasTools: boolean = true;
  @Input() enableGridToggle: boolean = true;
  @Input() enablePanMode: boolean = true;
  @Input() enableSelectionMode: boolean = true;
  @Input() enableZoomControls: boolean = true;

  // Built-in input fields configuration
  @Input() showHeaderInputs: boolean = false;
  @Input() inputFieldsConfig: {
    agentName?: { enabled: boolean; placeholder?: string; required?: boolean };
    agentType?: { enabled: boolean; options?: SelectOption[]; defaultValue?: string };
    metadata?: { enabled: boolean; label?: string; statusText?: { saved: string; notSaved: string } };
  } = {};

  // Built-in metadata dropdown state
  isMetadataDropdownOpen = false;
  metadataStatus = 'Metadata Information not saved';

  // Form controls for built-in inputs
  agentNameControl = new FormControl('');
  agentTypeControl = new FormControl('individual');

  // Metadata form controls
  orgControl = new FormControl('');
  domainControl = new FormControl('');
  projectControl = new FormControl('');
  teamControl = new FormControl('');

  // Dropdown options
  dropdownValues: { [key: string]: SelectOption[] } = {
    org: [
      { value: 'ascendion', label: 'Ascendion' },
      { value: 'company2', label: 'Company 2' },
      { value: 'company3', label: 'Company 3' }
    ],
    domain: [],
    project: [],
    team: []
  };

  // Outputs
  @Output() nodeAdded = new EventEmitter<CanvasNode>();
  @Output() nodeRemoved = new EventEmitter<string>();
  @Output() nodeMoved = new EventEmitter<{nodeId: string, position: {x: number, y: number}}>();
  @Output() nodeSelected = new EventEmitter<string>();

  @Output() connectionStarted = new EventEmitter<{nodeId: string, handleType: 'source' | 'target', event: MouseEvent}>();
  @Output() connectionCreated = new EventEmitter<CanvasEdge>();
  @Output() canvasDropped = new EventEmitter<{event: DragEvent, position: {x: number, y: number}}>();
  @Output() viewportChanged = new EventEmitter<CanvasViewport>();
  @Output() undoAction = new EventEmitter<void>();
  @Output() redoAction = new EventEmitter<void>();
  @Output() resetAction = new EventEmitter<void>();
  @Output() primaryButtonClicked = new EventEmitter<void>();
  @Output() stateChanged = new EventEmitter<{nodes: CanvasNode[], edges: CanvasEdge[]}>();
  @Output() agentNameChanged = new EventEmitter<string>();
  @Output() agentTypeChanged = new EventEmitter<string>();
  @Output() metadataChanged = new EventEmitter<{org: string, domain: string, project: string, team: string}>();

  // Internal state
  selectedNodeId: string | null = null;
  tempConnection: TempConnection = { isActive: false };
  nodeConnectionPoints: NodeConnectionPoints = {};
  viewport: CanvasViewport = {
    zoom: 1,
    x: 0,
    y: 0,
    isDragging: false,
    lastMouseX: 0,
    lastMouseY: 0
  };

  // Internal history management
  private history: {nodes: CanvasNode[], edges: CanvasEdge[]}[] = [];
  private historyIndex: number = -1;
  private maxHistorySize: number = 50;
  private isRestoringState: boolean = false;

  // Canvas tool states
  canvasMode: CanvasToolMode = 'select'; // Start with select mode
  showGridDots: boolean = true;

  // Mouse function controls
  mouseInteractionsEnabled: boolean = true; // Enable mouse interactions by default

  // Toolbar drag state
  toolbarPosition = { x: 20, y: 0 }; // Will be set to bottom in ngAfterViewInit
  isToolbarDragging = false;
  toolbarDragOffset = { x: 0, y: 0 };

  constructor(private cdr: ChangeDetectorRef) {}

  // TrackBy function for nodes to maintain DOM element identity
  trackByNodeId(index: number, node: CanvasNode): string {
    return node.id;
  }

  ngOnChanges(changes: any): void {
    // Update internal state when inputs change (but not during state restoration)
    if (!this.isRestoringState) {
      if (changes.nodes && changes.nodes.currentValue) {
        this.nodes = [...changes.nodes.currentValue];
      }
      if (changes.edges && changes.edges.currentValue) {
        this.edges = [...changes.edges.currentValue];
      }

      // Update connection points when nodes change
      if (changes.nodes) {
        setTimeout(() => this.updateNodeConnectionPoints(), 50);
      }
    }
  }

  ngAfterViewInit(): void {
    this.setupCanvasNavigation();
    setTimeout(() => {
      this.updateNodeConnectionPoints();
      // Initialize history with current state
      this.saveToHistory();
      // Set initial toolbar position to bottom
      this.setInitialToolbarPosition();
      // Set initial cursor to default arrow
      this.setSelectMode();
    }, 100);
  }

  ngOnDestroy(): void {
    // Cleanup event listeners
    document.removeEventListener('keydown', this.handleKeyDown);
  }

  private setupCanvasNavigation(): void {
    const element = this.canvasContainer?.nativeElement;
    if (element) {
      document.addEventListener('keydown', this.handleKeyDown.bind(this));

      element.addEventListener('mouseup', () => {
        if (this.viewport.isDragging) {
          this.updateNodeConnectionPoints();
        }
      });
    }
  }

  private handleKeyDown = (event: KeyboardEvent): void => {
    if (event.key === ' ') {
      this.resetViewport();
      event.preventDefault();
    }
  }

  resetViewport(): void {
    this.viewport = {
      zoom: 1,
      x: 0,
      y: 0,
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0
    };

    this.updateNodeConnectionPoints();
    this.viewportChanged.emit(this.viewport);
    this.cdr.detectChanges();
  }

  onDragOver(event: DragEvent): void {
    if (!this.enableConnections || !this.mouseInteractionsEnabled) return;
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }

  onDrop(event: DragEvent): void {
    if (!this.mouseInteractionsEnabled) return;
    event.preventDefault();

    // Save history before adding new node
    this.saveHistoryBeforeAction();

    const canvasBounds = this.canvasContainer.nativeElement.getBoundingClientRect();
    const position = {
      x: (event.clientX - canvasBounds.left - this.viewport.x) / this.viewport.zoom,
      y: (event.clientY - canvasBounds.top - this.viewport.y) / this.viewport.zoom
    };

    const safePosition = {
      x: Math.max(0, position.x),
      y: Math.max(0, position.y)
    };

    this.canvasDropped.emit({ event, position: safePosition });
  }

  onNodeSelected(nodeId: string): void {
    this.selectedNodeId = nodeId;
    this.nodeSelected.emit(nodeId);
  }

  onNodeMoved(data: {nodeId: string, position: {x: number, y: number}}): void {
    // Save history before moving node
    this.saveHistoryBeforeAction();

    // Update node position in internal state
    const nodeIndex = this.nodes.findIndex(node => node.id === data.nodeId);
    if (nodeIndex !== -1) {
      this.nodes[nodeIndex] = {
        ...this.nodes[nodeIndex],
        position: data.position
      };
    }

    this.nodeMoved.emit(data);
    this.updateNodeConnectionPoints();
  }



  onStartConnection(data: {nodeId: string, handleType: 'source' | 'target', event: MouseEvent}): void {
    if (!this.enableConnections) return;

    const node = this.nodes.find(n => n.id === data.nodeId);
    if (!node) return;

    let handleX, handleY;
    const nodePos = node.position;
    const nodeWidth = node.data.width || 280;

    if (data.handleType === 'source') {
      handleX = nodePos.x + nodeWidth;
      handleY = nodePos.y + 30;
    } else {
      handleX = nodePos.x;
      handleY = nodePos.y + 30;
    }

    const canvasRect = this.canvasContainer.nativeElement.getBoundingClientRect();
    const targetX = (data.event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;
    const targetY = (data.event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;

    this.tempConnection = {
      isActive: true,
      sourceNodeId: data.nodeId,
      sourceHandleType: data.handleType,
      sourceX: handleX,
      sourceY: handleY,
      targetX,
      targetY
    };

    this.connectionStarted.emit(data);
  }

  onDeleteNode(nodeId: string): void {
    // Save history before deleting node
    this.saveHistoryBeforeAction();

    // Remove node from internal state
    this.nodes = this.nodes.filter(node => node.id !== nodeId);

    // Remove any edges connected to this node
    this.edges = this.edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId);

    this.nodeRemoved.emit(nodeId);
    if (this.selectedNodeId === nodeId) {
      this.selectedNodeId = null;
    }

    this.updateNodeConnectionPoints();
  }

  onCanvasMouseMove(event: MouseEvent): void {
    // Handle canvas panning
    if (this.viewport.isDragging && this.enablePan) {
      const deltaX = event.clientX - this.viewport.lastMouseX;
      const deltaY = event.clientY - this.viewport.lastMouseY;

      this.viewport.x += deltaX;
      this.viewport.y += deltaY;

      this.viewport.lastMouseX = event.clientX;
      this.viewport.lastMouseY = event.clientY;

      this.cdr.detectChanges();
      return;
    }

    if (!this.tempConnection.isActive) return;

    // Update the temporary connection endpoint
    const canvasRect = this.canvasContainer.nativeElement.getBoundingClientRect();
    const targetX = (event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;
    const targetY = (event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;

    this.tempConnection = {
      ...this.tempConnection,
      targetX,
      targetY
    };

    this.cdr.detectChanges();
  }

  onCanvasWheel(event: WheelEvent): void {
    if (!this.enableZoom || !this.mouseInteractionsEnabled) return;
    event.preventDefault();

    const delta = -event.deltaY;
    const zoomSpeed = 0.001;
    const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.viewport.zoom + delta * zoomSpeed));

    if (newZoom !== this.viewport.zoom) {
      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      const zoomRatio = newZoom / this.viewport.zoom;
      const newX = mouseX - (mouseX - this.viewport.x) * zoomRatio;
      const newY = mouseY - (mouseY - this.viewport.y) * zoomRatio;

      this.viewport.zoom = newZoom;
      this.viewport.x = newX;
      this.viewport.y = newY;

      this.updateNodeConnectionPoints();
      this.viewportChanged.emit(this.viewport);
      this.cdr.detectChanges();
    }
  }

  onCanvasMouseDown(event: MouseEvent): void {
    if (!this.enablePan || !this.mouseInteractionsEnabled) return;

    // Start canvas dragging when:
    // 1. Middle mouse button is pressed
    // 2. Alt+left click
    // 3. Pan mode is active and left click
    if (event.button === 1 ||
        (event.button === 0 && event.altKey) ||
        (event.button === 0 && this.canvasMode === 'pan')) {
      this.viewport.isDragging = true;
      this.viewport.lastMouseX = event.clientX;
      this.viewport.lastMouseY = event.clientY;

      // Update cursor during drag
      if (this.canvasContainer) {
        this.canvasContainer.nativeElement.style.cursor = 'grabbing';
      }

      event.preventDefault();
    }
  }

  onCanvasMouseUp(event: MouseEvent): void {
    this.viewport.isDragging = false;

    // Restore cursor based on current mode
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor = this.canvasMode === 'pan' ? 'grab' : 'default';
    }

    if (!this.tempConnection.isActive || !this.enableConnections) return;

    const target = event.target as HTMLElement;
    const handleElement = target.closest('.handle');

    if (handleElement && this.tempConnection.sourceNodeId) {
      const sourceNodeId = this.tempConnection.sourceNodeId;
      const sourceHandleType = this.tempConnection.sourceHandleType;

      const nodeElement = handleElement.closest('[data-node-id]');
      if (nodeElement) {
        const targetNodeId = nodeElement.getAttribute('data-node-id');
        const isLeftHandle = handleElement.classList.contains('handle-left');
        const targetHandleType = isLeftHandle ? 'target' : 'source';

        if (targetNodeId && sourceNodeId !== targetNodeId &&
            ((sourceHandleType === 'source' && targetHandleType === 'target') ||
             (sourceHandleType === 'target' && targetHandleType === 'source'))) {

          let source = sourceNodeId;
          let target = targetNodeId;

          if (sourceHandleType === 'target') {
            source = targetNodeId;
            target = sourceNodeId;
          }

          // Save history before creating connection
          this.saveHistoryBeforeAction();

          const newEdge: CanvasEdge = {
            id: `${source}-${target}`,
            source,
            target,
            animated: true
          };

          // Add edge to internal state
          this.edges.push(newEdge);

          this.connectionCreated.emit(newEdge);
        }
      }
    }

    this.tempConnection = { isActive: false };
  }

  updateNodeConnectionPoints(): void {
    this.nodeConnectionPoints = {};

    for (const node of this.nodes) {
      const nodePos = node.position;
      const nodeWidth = node.data.width || 160;
      const nodeHeight = 40;
      const centerX = nodePos.x + nodeWidth / 2;
      const centerY = nodePos.y + nodeHeight / 2;

      this.nodeConnectionPoints[node.id] = {
        top: { x: centerX, y: nodePos.y },
        right: { x: nodePos.x + nodeWidth, y: centerY },
        bottom: { x: centerX, y: nodePos.y + nodeHeight },
        left: { x: nodePos.x, y: centerY }
      };
    }

    this.cdr.detectChanges();
  }

  getEdgePath(edge: CanvasEdge): string {
    if (Object.keys(this.nodeConnectionPoints).length === 0) {
      this.updateNodeConnectionPoints();
    }

    const sourcePoints = this.nodeConnectionPoints[edge.source];
    const targetPoints = this.nodeConnectionPoints[edge.target];

    if (!sourcePoints || !targetPoints) {
      return '';
    }

    // Find the nearest connection points between source and target
    const { sourcePoint, targetPoint } = this.findNearestConnectionPoints(sourcePoints, targetPoints);

    // Adjust target position to account for arrow marker size
    const arrowOffset = 8;
    const direction = this.getConnectionDirection(sourcePoint, targetPoint);
    const adjustedTarget = this.adjustTargetForArrow(targetPoint, direction, arrowOffset);

    // Create path based on connection direction
    return this.createConnectionPath(sourcePoint, adjustedTarget);
  }

  private findNearestConnectionPoints(sourcePoints: any, targetPoints: any): {sourcePoint: {x: number, y: number}, targetPoint: {x: number, y: number}} {
    let minDistance = Infinity;
    let bestSourcePoint = sourcePoints.right;
    let bestTargetPoint = targetPoints.left;

    // Check all combinations of connection points
    const sourceKeys = ['top', 'right', 'bottom', 'left'];
    const targetKeys = ['top', 'right', 'bottom', 'left'];

    for (const sourceKey of sourceKeys) {
      for (const targetKey of targetKeys) {
        const sourcePoint = sourcePoints[sourceKey];
        const targetPoint = targetPoints[targetKey];

        const distance = Math.sqrt(
          Math.pow(targetPoint.x - sourcePoint.x, 2) +
          Math.pow(targetPoint.y - sourcePoint.y, 2)
        );

        if (distance < minDistance) {
          minDistance = distance;
          bestSourcePoint = sourcePoint;
          bestTargetPoint = targetPoint;
        }
      }
    }

    return { sourcePoint: bestSourcePoint, targetPoint: bestTargetPoint };
  }

  private getConnectionDirection(sourcePoint: {x: number, y: number}, targetPoint: {x: number, y: number}): string {
    const dx = targetPoint.x - sourcePoint.x;
    const dy = targetPoint.y - sourcePoint.y;

    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left';
    } else {
      return dy > 0 ? 'bottom' : 'top';
    }
  }

  private adjustTargetForArrow(targetPoint: {x: number, y: number}, direction: string, offset: number): {x: number, y: number} {
    switch (direction) {
      case 'right': return { x: targetPoint.x - offset, y: targetPoint.y };
      case 'left': return { x: targetPoint.x + offset, y: targetPoint.y };
      case 'bottom': return { x: targetPoint.x, y: targetPoint.y - offset };
      case 'top': return { x: targetPoint.x, y: targetPoint.y + offset };
      default: return targetPoint;
    }
  }

  private createConnectionPath(sourcePoint: {x: number, y: number}, targetPoint: {x: number, y: number}): string {
    const dx = Math.abs(targetPoint.x - sourcePoint.x);
    const dy = Math.abs(targetPoint.y - sourcePoint.y);

    // If points are close, create a straight line
    if (dx < 20 && dy < 20) {
      return `M ${sourcePoint.x} ${sourcePoint.y} L ${targetPoint.x} ${targetPoint.y}`;
    }

    // Create right-angled path
    const midX = sourcePoint.x + (targetPoint.x - sourcePoint.x) / 2;
    const midY = sourcePoint.y + (targetPoint.y - sourcePoint.y) / 2;

    // Choose path based on which direction has more distance
    if (dx > dy) {
      // Horizontal priority
      return `M ${sourcePoint.x} ${sourcePoint.y} L ${midX} ${sourcePoint.y} L ${midX} ${targetPoint.y} L ${targetPoint.x} ${targetPoint.y}`;
    } else {
      // Vertical priority
      return `M ${sourcePoint.x} ${sourcePoint.y} L ${sourcePoint.x} ${midY} L ${targetPoint.x} ${midY} L ${targetPoint.x} ${targetPoint.y}`;
    }
  }

  getTempConnectionPath(): string {
    if (!this.tempConnection.isActive ||
        this.tempConnection.sourceX === undefined ||
        this.tempConnection.sourceY === undefined ||
        this.tempConnection.targetX === undefined ||
        this.tempConnection.targetY === undefined) {
      return '';
    }

    const sourceX = this.tempConnection.sourceX;
    const sourceY = this.tempConnection.sourceY;
    const targetX = this.tempConnection.targetX;
    const targetY = this.tempConnection.targetY;

    // Adjust target position for arrow visibility
    const arrowOffset = 8;
    const adjustedTargetX = targetX - arrowOffset;

    // Sharp temporary connection with proper arrow positioning
    if (Math.abs(sourceY - targetY) < 5) {
      // Same horizontal level - straight line
      return `M ${sourceX} ${sourceY} L ${adjustedTargetX} ${targetY}`;
    } else {
      // Different levels - create right-angled path
      const midX = sourceX + (adjustedTargetX - sourceX) / 2;
      return `M ${sourceX} ${sourceY} L ${midX} ${sourceY} L ${midX} ${targetY} L ${adjustedTargetX} ${targetY}`;
    }
  }

  // Toolbar actions
  onUndo(): void {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      const state = this.history[this.historyIndex];
      this.restoreState(state);
      console.log('Undo - History index:', this.historyIndex, 'Total history:', this.history.length);
    } else {
      console.log('Cannot undo - at beginning of history');
    }
    this.undoAction.emit();
  }

  onRedo(): void {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      const state = this.history[this.historyIndex];
      this.restoreState(state);
      console.log('Redo - History index:', this.historyIndex, 'Total history:', this.history.length);
    } else {
      console.log('Cannot redo - at end of history');
    }
    this.redoAction.emit();
  }

  onReset(): void {
    // Save current state before reset
    this.saveToHistory();

    // Clear all nodes and edges
    this.nodes = [];
    this.edges = [];
    this.selectedNodeId = null;
    this.tempConnection = { isActive: false };

    // Reset viewport
    this.resetViewport();

    // Emit state change to parent component
    this.stateChanged.emit({
      nodes: [],
      edges: []
    });

    // Emit events
    this.nodeRemoved.emit('all'); // Special case for clearing all
    this.resetAction.emit();
  }

  onPrimaryButtonClick(): void {
    this.primaryButtonClicked.emit();
  }

  // History management methods
  private saveToHistory(): void {
    // Don't save history during state restoration
    if (this.isRestoringState) return;

    // Remove any history after current index (when we're not at the end)
    this.history = this.history.slice(0, this.historyIndex + 1);

    // Add current state to history
    const currentState = {
      nodes: JSON.parse(JSON.stringify(this.nodes)),
      edges: JSON.parse(JSON.stringify(this.edges))
    };

    this.history.push(currentState);
    this.historyIndex = this.history.length - 1;

    console.log('Saved to history - Index:', this.historyIndex, 'Total:', this.history.length, 'Nodes:', currentState.nodes.length, 'Edges:', currentState.edges.length);

    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
      this.historyIndex--;
    }
  }

  private restoreState(state: {nodes: CanvasNode[], edges: CanvasEdge[]}): void {
    this.isRestoringState = true;

    // Update the internal state
    this.nodes = [...state.nodes];
    this.edges = [...state.edges];

    // Clear selection and temp connections
    this.selectedNodeId = null;
    this.tempConnection = { isActive: false };

    // Update connection points
    this.updateNodeConnectionPoints();

    // Emit state change to parent component
    this.stateChanged.emit({
      nodes: [...this.nodes],
      edges: [...this.edges]
    });

    // Reset flag after a short delay
    setTimeout(() => {
      this.isRestoringState = false;
    }, 100);
  }

  // Save history before actions
  private saveHistoryBeforeAction(): void {
    this.saveToHistory();
  }

  // Public methods for external components to add/remove nodes and edges
  addNode(node: CanvasNode): void {
    this.saveHistoryBeforeAction();
    this.nodes.push(node);
    this.updateNodeConnectionPoints();
    this.nodeAdded.emit(node);
  }

  addEdge(edge: CanvasEdge): void {
    this.saveHistoryBeforeAction();
    this.edges.push(edge);
  }

  removeNode(nodeId: string): void {
    this.onDeleteNode(nodeId);
  }

  removeEdge(edgeId: string): void {
    this.saveHistoryBeforeAction();
    this.edges = this.edges.filter(edge => edge.id !== edgeId);
  }

  // Canvas tool methods
  toggleGrid(): void {
    this.showGridDots = !this.showGridDots;
    this.showGrid = this.showGridDots;
  }

  setPanMode(): void {
    this.canvasMode = 'pan';
    this.mouseInteractionsEnabled = true;
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor = 'grab';
    }
  }

  setSelectMode(): void {
    this.canvasMode = 'select';
    this.mouseInteractionsEnabled = true;
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor = 'default';
    }
  }

  setZoomMode(): void {
    this.canvasMode = 'zoom';
    this.mouseInteractionsEnabled = true;
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor = 'zoom-in';
    }
  }

  disableMouseInteractions(): void {
    this.canvasMode = 'disabled';
    this.mouseInteractionsEnabled = false;
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor = 'not-allowed';
    }
  }

  zoomIn(): void {
    const newZoom = Math.min(this.maxZoom, this.viewport.zoom * 1.2);
    this.setZoom(newZoom);
  }

  zoomOut(): void {
    const newZoom = Math.max(this.minZoom, this.viewport.zoom / 1.2);
    this.setZoom(newZoom);
  }

  private setZoom(newZoom: number): void {
    if (newZoom !== this.viewport.zoom) {
      // Get canvas center for zoom
      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;

      // Calculate new position to zoom towards center
      const zoomRatio = newZoom / this.viewport.zoom;
      const newX = centerX - (centerX - this.viewport.x) * zoomRatio;
      const newY = centerY - (centerY - this.viewport.y) * zoomRatio;

      // Update viewport
      this.viewport.zoom = newZoom;
      this.viewport.x = newX;
      this.viewport.y = newY;

      // Update connection points
      this.updateNodeConnectionPoints();

      // Emit viewport change
      this.viewportChanged.emit(this.viewport);
    }
  }

  // Built-in input field methods
  onAgentNameChange(value: string): void {
    this.agentNameControl.setValue(value);
    this.agentNameChanged.emit(value);
  }

  onAgentTypeChange(value: string | string[]): void {
    const finalValue = Array.isArray(value) ? value[0] : value;
    this.agentTypeControl.setValue(finalValue);
    this.agentTypeChanged.emit(finalValue);
  }

  // Built-in metadata dropdown methods
  toggleMetadataDropdown(): void {
    this.isMetadataDropdownOpen = !this.isMetadataDropdownOpen;
  }

  closeMetadataDropdown(): void {
    this.isMetadataDropdownOpen = false;
  }

  onDropdownSelect(selectedValue: string | string[], level: number, currentType: string, nextType: string): void {
    const value = Array.isArray(selectedValue) ? selectedValue[0] : selectedValue;
    if (!value) return;

    // Update the current dropdown value
    switch (currentType) {
      case 'org':
        this.orgControl.setValue(value);
        // Reset dependent dropdowns
        this.domainControl.setValue('');
        this.projectControl.setValue('');
        this.teamControl.setValue('');
        // Load domain options
        this.loadDomainOptions(value);
        break;
      case 'domain':
        this.domainControl.setValue(value);
        // Reset dependent dropdowns
        this.projectControl.setValue('');
        this.teamControl.setValue('');
        // Load project options
        this.loadProjectOptions(this.orgControl.value || '', value);
        break;
      case 'project':
        this.projectControl.setValue(value);
        // Reset dependent dropdown
        this.teamControl.setValue('');
        // Load team options
        this.loadTeamOptions(this.orgControl.value || '', this.domainControl.value || '', value);
        break;
      case 'team':
        this.teamControl.setValue(value);
        break;
    }
  }

  applyMetadata(): void {
    const hasValues = this.orgControl.value || this.domainControl.value ||
                     this.projectControl.value || this.teamControl.value;

    if (hasValues) {
      this.metadataStatus = this.inputFieldsConfig.metadata?.statusText?.saved || 'Metadata Information saved';
    } else {
      this.metadataStatus = this.inputFieldsConfig.metadata?.statusText?.notSaved || 'Metadata Information not saved';
    }

    // Emit metadata change event
    this.metadataChanged.emit({
      org: this.orgControl.value || '',
      domain: this.domainControl.value || '',
      project: this.projectControl.value || '',
      team: this.teamControl.value || ''
    });

    this.closeMetadataDropdown();
  }

  cancelMetadata(): void {
    this.closeMetadataDropdown();
  }

  // Load dropdown options (same API pattern as nav-item)
  private loadDomainOptions(org: string): void {
    const domainOptionsMap: {[key: string]: SelectOption[]} = {
      'ascendion': [
        { value: 'engineering', label: 'Engineering' },
        { value: 'marketing', label: 'Marketing' },
        { value: 'sales', label: 'Sales' }
      ],
      'company2': [
        { value: 'tech', label: 'Technology' },
        { value: 'operations', label: 'Operations' }
      ],
      'company3': [
        { value: 'research', label: 'Research' },
        { value: 'development', label: 'Development' }
      ]
    };

    this.dropdownValues['domain'] = domainOptionsMap[org] || [];
  }

  private loadProjectOptions(org: string, domain: string): void {
    const projectOptions: SelectOption[] = [
      { value: 'project1', label: 'Project Alpha' },
      { value: 'project2', label: 'Project Beta' },
      { value: 'project3', label: 'Project Gamma' }
    ];

    this.dropdownValues['project'] = projectOptions;
  }

  private loadTeamOptions(org: string, domain: string, project: string): void {
    const teamOptions: SelectOption[] = [
      { value: 'team1', label: 'Team Alpha' },
      { value: 'team2', label: 'Team Beta' },
      { value: 'team3', label: 'Team Gamma' }
    ];

    this.dropdownValues['team'] = teamOptions;
  }

  // Toolbar drag methods
  onToolbarDragStart(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();

    this.isToolbarDragging = true;

    // Calculate offset from mouse to toolbar position
    this.toolbarDragOffset.x = event.clientX - this.toolbarPosition.x;
    this.toolbarDragOffset.y = event.clientY - this.toolbarPosition.y;

    // Add global mouse event listeners
    document.addEventListener('mousemove', this.onToolbarDrag);
    document.addEventListener('mouseup', this.onToolbarDragEnd);

    // Change cursor
    document.body.style.cursor = 'grabbing';
  }

  onToolbarDrag = (event: MouseEvent): void => {
    if (!this.isToolbarDragging) return;

    event.preventDefault();

    // Calculate new position
    const newX = event.clientX - this.toolbarDragOffset.x;
    const newY = event.clientY - this.toolbarDragOffset.y;

    // Get canvas bounds to constrain toolbar within canvas
    const canvasRect = this.canvasContainer?.nativeElement.getBoundingClientRect();
    if (canvasRect) {
      // Constrain to canvas bounds (with some padding)
      const padding = 10;
      const toolbarWidth = 200; // Approximate toolbar width
      const toolbarHeight = 50; // Approximate toolbar height

      this.toolbarPosition.x = Math.max(padding, Math.min(canvasRect.width - toolbarWidth - padding, newX));
      this.toolbarPosition.y = Math.max(padding, Math.min(canvasRect.height - toolbarHeight - padding, newY));
    } else {
      this.toolbarPosition.x = newX;
      this.toolbarPosition.y = newY;
    }

    this.cdr.detectChanges();
  }

  onToolbarDragEnd = (): void => {
    this.isToolbarDragging = false;

    // Remove global mouse event listeners
    document.removeEventListener('mousemove', this.onToolbarDrag);
    document.removeEventListener('mouseup', this.onToolbarDragEnd);

    // Reset cursor
    document.body.style.cursor = '';
  }

  // Set initial toolbar position to bottom
  private setInitialToolbarPosition(): void {
    const canvasRect = this.canvasContainer?.nativeElement.getBoundingClientRect();
    if (canvasRect) {
      const toolbarHeight = 50; // Approximate toolbar height
      const padding = 20;

      // Position at bottom left with padding
      this.toolbarPosition.x = padding;
      this.toolbarPosition.y = canvasRect.height - toolbarHeight - padding;

      this.cdr.detectChanges();
    }
  }
}
