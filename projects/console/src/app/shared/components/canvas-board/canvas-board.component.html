<div class="canvas-board-container">
  <!-- Built-in Header Inputs -->
  <div class="header-inputs-section" *ngIf="showHeaderInputs">
    <div class="header-inputs-container">
      <!-- Agent Name Input -->
      <div class="input-group" *ngIf="inputFieldsConfig.agentName?.enabled">
        <input
          type="text"
          [placeholder]="inputFieldsConfig.agentName?.placeholder || 'Agent Name'"
          [required]="inputFieldsConfig.agentName?.required || false"
          [value]="agentNameControl.value"
          (input)="onAgentNameChange($any($event.target).value)"
          class="agent-name-input"
        />
        <svg class="edit-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
        </svg>
      </div>

      <!-- Agent Type Dropdown -->
      <div class="input-group" *ngIf="inputFieldsConfig.agentType?.enabled">
        <app-select-dropdown
          [options]="inputFieldsConfig.agentType?.options || [{value: 'individual', label: 'Individual Agent'}, {value: 'collaborative', label: 'Collaborative Agent'}]"
          [control]="agentTypeControl"
          placeholder="Individual Agent"
          [dropdownWidth]="'200px'"
          [dropdownHeight]="'40px'"
          (selectionChange)="onAgentTypeChange($event)"
        ></app-select-dropdown>
      </div>

      <!-- Metadata Dropdown -->
      <div class="input-group metadata-dropdown-container" *ngIf="inputFieldsConfig.metadata?.enabled">
        <div class="metadata-dropdown-trigger" (click)="toggleMetadataDropdown()">
          <span>{{ inputFieldsConfig.metadata?.label || 'Metadata Info' }}</span>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="6,9 12,15 18,9"></polyline>
          </svg>
        </div>
        <span class="metadata-status">{{ metadataStatus }}</span>

        <!-- Metadata Popover -->
        <div *ngIf="isMetadataDropdownOpen" class="metadata-popover">
          <form>
            <div class="dropdown-row-vertical">
              <app-select-dropdown
                [options]="dropdownValues['org']"
                [label]="'Organization'"
                [placeholder]="'Select Organization'"
                [dropdownWidth]="'260px'"
                [dropdownHeight]="'56px'"
                [disabled]="false"
                [required]="true"
                [control]="orgControl"
                (selectionChange)="onDropdownSelect($event, 1, 'org', 'domain')"
              ></app-select-dropdown>
              <app-select-dropdown
                [options]="dropdownValues['domain']"
                [label]="'Domain'"
                [placeholder]="'Select Domain'"
                [dropdownWidth]="'260px'"
                [dropdownHeight]="'56px'"
                [disabled]="!orgControl.value"
                [required]="true"
                [control]="domainControl"
                (selectionChange)="onDropdownSelect($event, 2, 'domain', 'project')"
              ></app-select-dropdown>
              <app-select-dropdown
                [options]="dropdownValues['project']"
                [label]="'Project'"
                [placeholder]="'Select Project'"
                [dropdownWidth]="'260px'"
                [dropdownHeight]="'56px'"
                [disabled]="!domainControl.value"
                [required]="true"
                [control]="projectControl"
                (selectionChange)="onDropdownSelect($event, 3, 'project', 'team')"
              ></app-select-dropdown>
              <app-select-dropdown
                [options]="dropdownValues['team']"
                [label]="'Team'"
                [placeholder]="'Select Team'"
                [dropdownWidth]="'260px'"
                [dropdownHeight]="'56px'"
                [disabled]="!projectControl.value"
                [required]="true"
                [control]="teamControl"
                (selectionChange)="onDropdownSelect($event, 4, 'team', 'not-found')"
              ></app-select-dropdown>
            </div>
            <div class="popover-actions">
              <button type="button" class="cancel-btn" (click)="cancelMetadata()">Cancel</button>
              <button type="button" class="apply-btn" (click)="applyMetadata()">Apply</button>
            </div>
          </form>
        </div>

        <!-- Backdrop -->
        <div *ngIf="isMetadataDropdownOpen" class="metadata-backdrop" (click)="closeMetadataDropdown()"></div>
      </div>
    </div>
  </div>



  <!-- Navigation hints -->
  <div class="navigation-hint" *ngIf="navigationHints.length > 0">
    <span *ngFor="let hint of navigationHints; let last = last">
      {{ hint }}<span *ngIf="!last"> | </span>
    </span>
  </div>

  <!-- Canvas container -->
  <div
    class="canvas-container"
    #canvasContainer
    [class.show-grid]="showGrid"
    [class.disabled]="!mouseInteractionsEnabled"
    (dragover)="onDragOver($event)"
    (drop)="onDrop($event)"
    (mouseup)="onCanvasMouseUp($event)"
    (mousemove)="onCanvasMouseMove($event)"
    (wheel)="onCanvasWheel($event)"
    (mousedown)="onCanvasMouseDown($event)"
  >
    <!-- Canvas Tools Toolbar - positioned dynamically -->
    <div class="canvas-tools-toolbar"
         *ngIf="showCanvasTools"
         [style.left.px]="toolbarPosition.x"
         [style.top.px]="toolbarPosition.y">
      <!-- Drag Handle (6-dot grid) -->
      <button class="tool-btn drag-handle"
              title="Move Toolbar"
              aria-label="Move Toolbar"
              (mousedown)="onToolbarDragStart($event)">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="9" cy="6" r="1.5"></circle>
          <circle cx="15" cy="6" r="1.5"></circle>
          <circle cx="9" cy="12" r="1.5"></circle>
          <circle cx="15" cy="12" r="1.5"></circle>
          <circle cx="9" cy="18" r="1.5"></circle>
          <circle cx="15" cy="18" r="1.5"></circle>
        </svg>
      </button>

      <!-- Selection Mode -->
      <button class="tool-btn"
              [class.active]="canvasMode === 'select'"
              (click)="setSelectMode()"
              title="Selection Mode"
              aria-label="Selection Mode">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"></path>
          <path d="M13 13l6 6"></path>
        </svg>
      </button>

      <!-- Pan Mode -->
      <button class="tool-btn"
              [class.active]="canvasMode === 'pan'"
              (click)="setPanMode()"
              title="Pan Mode"
              aria-label="Pan Mode">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 11V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v5"></path>
          <path d="M14 10V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v6"></path>
          <path d="M10 10.5V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v8"></path>
          <path d="M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.83L7 15"></path>
        </svg>
      </button>

      <!-- Zoom Mode -->
      <button class="tool-btn"
              [class.active]="canvasMode === 'zoom'"
              (click)="setZoomMode()"
              title="Zoom Mode"
              aria-label="Zoom Mode">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="M21 21l-4.35-4.35"></path>
          <line x1="11" y1="8" x2="11" y2="14"></line>
          <line x1="8" y1="11" x2="14" y2="11"></line>
        </svg>
      </button>

      <!-- Zoom In -->
      <button class="tool-btn"
              (click)="zoomIn(); setZoomMode()"
              title="Zoom In"
              aria-label="Zoom In">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="M21 21l-4.35-4.35"></path>
          <line x1="11" y1="8" x2="11" y2="14"></line>
          <line x1="8" y1="11" x2="14" y2="11"></line>
        </svg>
      </button>

      <!-- Zoom Out -->
      <button class="tool-btn"
              (click)="zoomOut(); setZoomMode()"
              title="Zoom Out"
              aria-label="Zoom Out">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="M21 21l-4.35-4.35"></path>
          <line x1="8" y1="11" x2="14" y2="11"></line>
        </svg>
      </button>
    </div>

    <!-- Floating Toolbar - positioned in top-right corner -->
    <div class="floating-toolbar" *ngIf="showToolbar">
      <button
        *ngIf="enableUndo"
        class="toolbar-btn"
        (click)="onUndo()"
        title="Undo"
        aria-label="Undo">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 7v6h6"></path>
          <path d="M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13"></path>
        </svg>
      </button>

      <button
        *ngIf="enableRedo"
        class="toolbar-btn"
        (click)="onRedo()"
        title="Redo"
        aria-label="Redo">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 7v6h-6"></path>
          <path d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7"></path>
        </svg>
      </button>

      <button
        *ngIf="enableReset"
        class="toolbar-btn"
        (click)="onReset()"
        title="Reset"
        aria-label="Reset">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4.01 7.58 4.01 12C4.01 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"></path>
        </svg>
      </button>

      <button
        class="primary-btn"
        (click)="onPrimaryButtonClick()"
        [attr.aria-label]="primaryButtonText">
        <span *ngIf="primaryButtonIcon" [innerHTML]="primaryButtonIcon"></span>
        {{ primaryButtonText }}
      </button>
    </div>
    <!-- Viewport container that applies the transform -->
    <div
      class="canvas-viewport"
      [style.transform]="'translate(' + viewport.x + 'px, ' + viewport.y + 'px) scale(' + viewport.zoom + ')'"
    >
      <!-- All elements in the same transformed container -->
      <div class="canvas-content">
        <!-- Connections between nodes -->
        <svg class="connections-layer" *ngIf="enableConnections">
          <!-- Static connections -->
          <path
            *ngFor="let edge of edges"
            [attr.d]="getEdgePath(edge)"
            [attr.id]="edge.id"
            class="edge-path"
            [class.animated]="edge.animated"
            [style.stroke]="connectionColor"
            marker-end="url(#arrow)"
          ></path>

          <!-- Arrow marker definition -->
          <defs>
            <marker
              id="arrow"
              viewBox="0 0 12 12"
              refX="10"
              refY="6"
              markerWidth="8"
              markerHeight="8"
              orient="auto"
              markerUnits="strokeWidth"
            >
              <path d="M 2 2 L 10 6 L 2 10 z" class="arrow-head" [style.fill]="connectionColor"></path>
            </marker>
          </defs>
        </svg>

        <!-- Nodes container -->
        <div class="nodes-container">
          <!-- Render nodes using content projection with trackBy -->
          <ng-container *ngFor="let node of nodes; trackBy: trackByNodeId">
            <ng-container
              *ngTemplateOutlet="nodeTemplate; context: {
                $implicit: node,
                selected: selectedNodeId === node.id,
                onDelete: onDeleteNode.bind(this),
                onMove: onNodeMoved.bind(this),
                onSelect: onNodeSelected.bind(this),
                onStartConnection: onStartConnection.bind(this),
                mouseInteractionsEnabled: mouseInteractionsEnabled,
                canvasMode: canvasMode
              }"
            ></ng-container>
          </ng-container>
        </div>

        <!-- Temporary connection line - render on top -->
        <svg *ngIf="tempConnection.isActive && enableConnections" class="temp-connection-layer">
          <path
            [attr.d]="getTempConnectionPath()"
            class="temp-connection-path"
            [style.stroke]="connectionColor"
            marker-end="url(#arrow)"
          ></path>
        </svg>

        <!-- Fallback message -->
        <div *ngIf="nodes.length === 0" class="no-nodes-message">
          {{ fallbackMessage }}
        </div>
      </div>
    </div>
  </div>
</div>
