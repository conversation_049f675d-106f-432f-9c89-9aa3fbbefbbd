import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ButtonComponent } from '@ava/play-comp-library';
import { SelectDropdownComponent } from '../select-dropdown/select-dropdown.component';
import { MetadataConfigService, MetadataDropdownConfig, MetadataValues } from '../../services/metadata-config.service';

@Component({
  selector: 'app-metadata-dropdown',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ButtonComponent, SelectDropdownComponent],
  templateUrl: './metadata-dropdown.component.html',
  styleUrls: ['./metadata-dropdown.component.scss']
})
export class MetadataDropdownComponent implements OnInit, OnDestroy {
  @Input() config: MetadataDropdownConfig = {
    enabled: true,
    label: 'Metadata Info',
    placeholder: 'Metadata Information not saved',
    showStatus: true,
    statusText: {
      saved: 'Metadata Information saved',
      notSaved: 'Metadata Information not saved'
    }
  };

  @Output() metadataChanged = new EventEmitter<MetadataValues>();

  isDropdownOpen = false;
  statusText = '';
  private subscription = new Subscription();

  constructor(public metadataService: MetadataConfigService) {}

  ngOnInit(): void {
    // Subscribe to metadata changes
    this.subscription.add(
      this.metadataService.getMetadataValues().subscribe(values => {
        this.updateStatusText();
        this.metadataChanged.emit(values);
      })
    );

    this.updateStatusText();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  onOrgChange(value: string | string[]): void {
    const orgValue = Array.isArray(value) ? value[0] : value;
    this.metadataService.orgControl.setValue(orgValue);
    
    // Reset dependent dropdowns
    this.metadataService.domainControl.setValue('');
    this.metadataService.projectControl.setValue('');
    this.metadataService.teamControl.setValue('');
    
    // Load domain options
    this.metadataService.loadDomainOptions(orgValue);
  }

  onDomainChange(value: string | string[]): void {
    const domainValue = Array.isArray(value) ? value[0] : value;
    this.metadataService.domainControl.setValue(domainValue);
    
    // Reset dependent dropdowns
    this.metadataService.projectControl.setValue('');
    this.metadataService.teamControl.setValue('');
    
    // Load project options
    const orgValue = this.metadataService.orgControl.value || '';
    this.metadataService.loadProjectOptions(orgValue, domainValue);
  }

  onProjectChange(value: string | string[]): void {
    const projectValue = Array.isArray(value) ? value[0] : value;
    this.metadataService.projectControl.setValue(projectValue);
    
    // Reset dependent dropdown
    this.metadataService.teamControl.setValue('');
    
    // Load team options
    const orgValue = this.metadataService.orgControl.value || '';
    const domainValue = this.metadataService.domainControl.value || '';
    this.metadataService.loadTeamOptions(orgValue, domainValue, projectValue);
  }

  onTeamChange(value: string | string[]): void {
    const teamValue = Array.isArray(value) ? value[0] : value;
    this.metadataService.teamControl.setValue(teamValue);
  }

  applyMetadata(): void {
    const values = this.metadataService.applyValues();
    this.updateStatusText();
    this.closeDropdown();
  }

  cancelMetadata(): void {
    // Reset to previous values if needed
    this.closeDropdown();
  }

  private updateStatusText(): void {
    if (this.config.showStatus) {
      this.statusText = this.metadataService.hasValues() 
        ? this.config.statusText.saved 
        : this.config.statusText.notSaved;
    }
  }
}
