<div class="ava-dropdown" [class.open]="isOpen" (keydown)="onDropdownKeyDown($event)">
  <!-- Dropdown Toggle -->
  <button type="button"
          class="dropdown-toggle"
          [class.open]="isOpen"
          [class.disabled]="disabled"
          [disabled]="disabled"
          (click)="onToggleClick($event)"
          (keydown)="onToggleKeyDown($event)"
          [attr.aria-expanded]="isOpen"
          [attr.aria-haspopup]="'listbox'"
          [attr.aria-label]="getDisplayText()">
    <span>{{ getDisplayText() }}</span>
    <ava-icon [iconName]="dropdownIcon" [iconSize]="16" [style.transform]="isOpen ? 'rotate(180deg)' : 'rotate(0deg)'"></ava-icon>
  </button>

  <!-- Dropdown Menu -->
  <div class="dropdown-menu"
       *ngIf="isOpen"
       role="listbox"
       [attr.aria-label]="dropdownTitle">

    <!-- Search -->
    <div class="search-box" *ngIf="search || enableSearch">
      <input
        type="text"
        placeholder="Search..."
        [(ngModel)]="searchTerm"
        (input)="onSearch()"
        (keydown)="onSearchKeyDown($event)"
        [attr.aria-label]="'Search ' + dropdownTitle"
        tabindex="0"
        autocomplete="off">
      <ava-icon [iconName]="'search'" [iconSize]="14"></ava-icon>
    </div>

    <!-- Options -->
    <div class="options" role="group">
      <div *ngFor="let option of filteredOptions; let i = index" class="option-group">

        <!-- Main Option -->
        <div class="option"
             [class.selected]="isOptionSelected(option)"
             [class.has-suboptions]="hasSubOptions(option.name)"
             [class.checkbox-option]="checkboxOptions.includes(option.name)"
             [class.focused]="focusedOptionIndex === i"
             [class.disabled]="isOptionDisabled(option)"
             (click)="hasSubOptions(option.name) ? toggleSubOptions(option.name) : selectOption(option); $event.stopPropagation()"
             (keydown)="onOptionKeyDown($event, option)"
             [attr.tabindex]="focusedOptionIndex === i ? '0' : '-1'"
             [attr.role]="'option'"
             [attr.aria-selected]="isOptionSelected(option)"
             [attr.aria-expanded]="hasSubOptions(option.name) ? (expandedOption === option.name) : null"
             [attr.aria-label]="option.name"
             [attr.aria-disabled]="isOptionDisabled(option)">

          <!-- With Checkbox -->
          <ava-checkbox
            *ngIf="checkboxOptions.includes(option.name)"
            variant="with-bg"
            [isChecked]="isOptionSelected(option)"
            [label]="option.name"
            [disable]="isOptionDisabled(option)">
          </ava-checkbox>

          <!-- Without Checkbox -->
          <div *ngIf="!checkboxOptions.includes(option.name)" class="option-content">
            <ava-icon
              *ngIf="shouldShowIcon(option)"
              [iconName]="getOptionIcon(option)"
              [iconSize]="16">
            </ava-icon>
            <span>{{ option.name }}</span>
          </div>

          <!-- Sub-options arrow: down by default, right when expanded -->
          <ava-icon
            *ngIf="hasSubOptions(option.name)"
            [iconName]="expandedOption === option.name ? 'chevron-right' : 'chevron-down'"
            [iconSize]="12"
            (click)="toggleSubOptions(option.name); $event.stopPropagation()">
          </ava-icon>
        </div>
      </div>
    </div>

    <!-- No results -->
    <div *ngIf="filteredOptions.length === 0" class="no-results">
      No options found
    </div>
  </div>

    <!-- Sub-options positioned next to hovered option -->
    <ng-container *ngFor="let option of filteredOptions; let optionIndex = index">
      <div class="suboptions-overlay"
           *ngIf="expandedOption === option.name && isOpen"
           [style.top.px]="getSuboptionPosition(optionIndex)"
           role="listbox"
           [attr.aria-label]="'Sub-options for ' + option.name">
        <div class="suboptions-panel">
          <div *ngFor="let subOption of suboptions[option.name]; let i = index"
               class="suboption"
               [class.selected]="isSubOptionSelected(subOption)"
               [class.focused]="focusedSubOptionIndex === i"
               [class.disabled]="isSubOptionDisabled(subOption)"
               (click)="selectSubOption(subOption); $event.stopPropagation()"
               (keydown)="onSubOptionKeyDown($event, subOption)"
               [attr.tabindex]="focusedSubOptionIndex === i ? '0' : '-1'"
               [attr.role]="'option'"
               [attr.aria-selected]="isSubOptionSelected(subOption)"
               [attr.aria-label]="subOption.name"
               [attr.aria-disabled]="isSubOptionDisabled(subOption)">
            <span>{{ subOption.name }}</span>
          </div>
        </div>
      </div>
    </ng-container>
  </div>
