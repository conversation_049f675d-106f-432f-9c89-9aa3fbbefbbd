import {
  Component,
  ElementRef,
  ViewChild,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import { ButtonComponent } from '@ava/play-comp-library';

declare const monaco: any;

export type CodeLanguage = 'python' | 'javascript' | 'typescript' | 'json' | 'sql' | 'html' | 'css' | 'markdown' | 'yaml' | 'xml' | 'plaintext';
export type CodeEditorTheme = 'light' | 'dark';

export interface EditorActionButton {
 
  label: string;
  style?: 'primary' | 'secondary' | 'outline' | 'text' | 'danger';
  customClass?: string;
  icon?: string;
}

/**
 * Monaco Code Editor Component
 *
 * @Input title - Editor title displayed in header
 * @Input value - Initial code value
 * @Input language - Programming language (python, javascript, etc.)
 * @Input theme - Editor theme (light/dark)
 * @Input height - Editor height (default: 400px)
 * @Input readonly - Make editor read-only
 * @Input Control - Angular FormControl for form integration
 * @Input actionButtons - Array of custom action buttons
 * @Input footerText - Footer note text
 *
 * @Output valueChange - Emits when code changes
 * @Output primaryButtonSelected - Emits when Run button clicked
 * @Output actionButtonClicked - Emits when action button clicked
 * @Output editorReady - Emits when editor is initialized
 *
 * Usage:
 * <app-code-editor
 *   title="Code Editor"
 *   language="python"
 *   [Control]="formControl"
 *   (primaryButtonSelected)="runCode()">
 * </app-code-editor>
 */
@Component({
  selector: 'app-code-editor',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './code-editor.component.html',
  styleUrls: ['./code-editor.component.scss']
})
export class CodeEditorComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  @ViewChild('editorContainer', { static: true }) private readonly editorContainer!: ElementRef;

  @Input() title = '';
  @Input() value = '';
  @Input() language: CodeLanguage = 'python';
  @Input() theme: CodeEditorTheme = 'light';
  @Input() height = '400px';
  @Input() readonly = false;
  @Input() customCssClass = '';
  @Input() placeholder = '';
  @Input() Control: FormControl | null = null;
  @Input() actionButtons: EditorActionButton[] = [];
  @Input() footerText = '';

  @Output() readonly valueChange = new EventEmitter<string>();
  @Output() readonly primaryButtonSelected = new EventEmitter<void>();
  @Output() readonly actionButtonClicked = new EventEmitter<number>();
  @Output() readonly editorReady = new EventEmitter<any>();

  readonly state = {
    loading: true,
    error: false,
    errorMessage: null as string | null,
    processing: false,
  };

  private editor: any = null;
  private valueChangeTimeout: any = null;

  constructor(private readonly cdr: ChangeDetectorRef) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (this.editor) {
      if (changes['value'] && changes['value'].currentValue !== this.editor.getValue()) {
        this.editor.setValue(changes['value'].currentValue || '');
      }
      if (changes['language'] && changes['language'].currentValue) {
        monaco.editor.setModelLanguage(this.editor.getModel(), changes['language'].currentValue);
      }
      if (changes['theme'] && changes['theme'].currentValue) {
        this.applyTheme();
      }
    }
  }

  ngOnInit(): void {
    this.configureMonacoEnvironment();
    if (this.Control?.value && !this.value) {
      this.value = this.Control.value;
    }
  }

  ngAfterViewInit(): void {
    setTimeout(() => this.initializeEditor(), 100);
  }

  ngOnDestroy(): void {
    this.editor?.dispose();
  }

  private configureMonacoEnvironment(): void {
    if (typeof window !== 'undefined' && !(window as any).MonacoEnvironment) {
      (window as any).MonacoEnvironment = {
        getWorkerUrl: () => './editor.worker.js'
      };
    }
  }

  async initializeEditor(): Promise<void> {
    try {
      if (this.editor) return;
      this.state.loading = true;
      this.state.error = false;
      this.state.errorMessage = null;
      this.cdr.markForCheck();

      const monacoModule = await import('monaco-editor');

      if (!this.editorContainer?.nativeElement) {
        throw new Error('Editor container not found');
      }

      this.editorContainer.nativeElement.style.height = this.height;
      this.defineCustomThemes(monacoModule);

      this.editor = monacoModule.editor.create(this.editorContainer.nativeElement, {
        value: this.value,
        language: this.language,
        theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light',
        automaticLayout: true,
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        readOnly: this.readonly,
        placeholder: this.placeholder,
        fontSize: 14,
        fontFamily: "'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Consolas', 'Courier New', monospace",
        tabSize: 4,
        lineHeight: 22,
        wordWrap: 'on',
        lineNumbers: 'on',
        folding: true,
        autoIndent: 'full',
        formatOnPaste: true,
        formatOnType: true,
        autoClosingBrackets: 'always',
        autoClosingQuotes: 'always',
        bracketPairColorization: { enabled: true },
        guides: { bracketPairs: true, indentation: true }
      });

      this.setupEventListeners();
      this.state.loading = false;
      this.editorReady.emit(this.editor);
      this.cdr.markForCheck();

    } catch (error) {
      this.state.loading = false;
      this.state.error = true;
      this.state.errorMessage = `Failed to initialize code editor: ${error}`;
      this.cdr.markForCheck();
    }
  }

  private defineCustomThemes(monaco: any): void {
    monaco.editor.defineTheme('code-editor-light', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6c757d', fontStyle: 'italic' },
        { token: 'keyword', foreground: '0066cc' },
        { token: 'string', foreground: '28a745' },
        { token: 'number', foreground: 'e83e8c' },
        { token: 'function', foreground: 'fd7e14' }
      ],
      colors: {
        'editor.background': '#ffffff',
        'editor.foreground': '#2c3e50',
        'editorLineNumber.foreground': '#6c757d',
        'editor.selectionBackground': '#007bff26',
        'editor.lineHighlightBackground': '#f8f9fa',
        'editorCursor.foreground': '#007bff'
      }
    });

    monaco.editor.defineTheme('code-editor-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6c757d', fontStyle: 'italic' },
        { token: 'keyword', foreground: '66d9ef' },
        { token: 'string', foreground: 'a6e22e' },
        { token: 'number', foreground: 'fd971f' },
        { token: 'function', foreground: 'f92672' }
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#e9ecef',
        'editorLineNumber.foreground': '#6c757d',
        'editor.selectionBackground': '#66d9ef26',
        'editor.lineHighlightBackground': '#2c2c2c',
        'editorCursor.foreground': '#66d9ef'
      }
    });
  }

  private applyTheme(): void {
    if (this.editor) {
      this.editor.updateOptions({
        theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light'
      });
    }
  }

  private setupEventListeners(): void {
    if (!this.editor) return;

    this.editor.onDidChangeModelContent(() => {
      const currentValue = this.editor.getValue();
      if (this.valueChangeTimeout) clearTimeout(this.valueChangeTimeout);
      this.valueChangeTimeout = setTimeout(() => {
        this.valueChange.emit(currentValue);
        if (this.Control) {
          this.Control.setValue(currentValue);
        }
      }, 200);
    });
  }

  selectAll(): void {
    if (this.editor) {
      this.editor.setSelection(this.editor.getModel()!.getFullModelRange());
      this.editor.focus();
    }
  }

  clear(): void {
    if (this.editor) {
      this.editor.setValue('');
      this.editor.focus();
    }
  }

  showProcessingLoader(): void {
    this.state.processing = true;
    this.cdr.markForCheck();
  }

  hideProcessingLoader(): void {
    this.state.processing = false;
    this.cdr.markForCheck();
  }

  onPrimaryButtonClick(): void {
    this.showProcessingLoader();
    this.primaryButtonSelected.emit();
  }

  getValue(): string {
    return this.editor?.getValue() ?? '';
  }

  setValue(newValue: string): void {
    this.editor?.setValue(newValue);
  }

  setTheme(newTheme: CodeEditorTheme): void {
    this.theme = newTheme;
    this.applyTheme();
  }

  focus(): void {
    this.editor?.focus();
  }

  get isReady(): boolean {
    return !!this.editor && !this.state.loading;
  }

  onActionButtonClick(idx: number): void {
    this.actionButtonClicked.emit(idx);
  }
}
