<div class="approval">
    <div class="approval-left-screen">
        <!-- <ava-sidebar class="basic-sidebar-theme" width="280px" collapsedWidth="65px" height="500px"
            [class.basic-collapsed]="isBasicCollapsed" (collapseToggle)="onBasicCollapseToggle($event)">  
            <div slot="content">
                <div class="basic-nav-section">
                    <div *ngFor="let item of basicSidebarItems" class="basic-nav-item" [class.basic-active]="item.active"
                        (click)="onBasicItemClick(item)">
                        <ava-icon [iconName]="item.icon" class="basic-nav-icon"></ava-icon>
                        <span class="basic-nav-text">{{ item.text }}</span>
                    </div>
                </div>
            </div>
        </ava-sidebar> -->
        <div class="quick-actions-content" *ngIf="quickActionsExpanded">
            <div class="action-buttons">
                <button *ngFor="let action of quickActions" class="action-button" (click)="uClick(action.label)">
                    <div class="action-icon">
                        <img [src]="'/svgs/icons/' + action.icon + '.svg'" [alt]="action.label" width="24" height="24" />
                    </div>
                    <span class="action-label">{{ action.label }}</span>
                </button>
            </div>
        </div>
    </div>
    <div class="approval-right-screen">
        <div class="approval-title-filter">
            <ava-text-card [type]="'default'" [iconName]="'hourglass'" [title]="'Total Pending'" [value]="totalAgentsApprovals"
                [description]="'All agents awaiting approval'">
            </ava-text-card>
            <ava-text-card [type]="'default'" [iconName]="'shield-alert'" [title]="'High Priority'" [value]="totalHighPriorityApprovals"
                [description]="'Agents for immediate approval'">
            </ava-text-card>
            <ava-text-card [type]="'default'" [iconName]="'shield-alert'" [title]="'Mid Priority'" [value]="totalMidPriorityApprovals"
                [description]="'Agents with moderate urgency'">
            </ava-text-card>
            <ava-text-card [type]="'default'" [iconName]="'shield-alert'" [title]="'Low Priority'" [value]="totalLowPriorityApprovals"
                [description]="'Agents with low urgency'">
            </ava-text-card>
        </div>
        
    <div class="filter-section">
        <div class="search-bars">
            <ava-dropdown dropdownTitle="Search Categories" [options]="options" [search]="true"
                (selectionChange)="onSelectionChange($event)">
            </ava-dropdown>
            <ava-dropdown dropdownTitle="Search Options" [options]="options" [search]="true"
                (selectionChange)="onSelectionChange($event)">
            </ava-dropdown>
            <ava-button label="Bulk Approve" (userClick)="uClick(1)" variant="primary" size="large" state="default"
                iconPosition="left"></ava-button>
        </div>
        <div class="textbox section">
            <ava-textbox placeholder="Search..." [(ngModel)]="searchValue">
                <ava-icon slot="icon-start" iconName="search" [iconSize]="16" iconColor="var(--color-brand-primary)">
                </ava-icon>
            </ava-textbox>
        </div>
    </div>
        
        <div class="approval-card-section">
            <div class="approval-card-header">
                All - {{totalAgentsApprovals}} Agents
            </div>
            <ng-template let-i="index" let-label="label" #footerTemplate>
                <div class="footer-content">
                    <div class="footer-right">
                        <ava-button label="Test" (userClick)="uClick(i)" variant="secondary" size="medium" state="default"
                            iconName="play" iconPosition="left"></ava-button>
                        <ava-button label="Sendback" (userClick)="uClick(i)" variant="secondary" size="medium" state="default"
                            iconName="move-left" iconPosition="left"></ava-button>
                        <ava-button label="Approve" (userClick)="uClick(i)" variant="primary" size="medium" state="default"
                            iconName="Check" iconPosition="left"></ava-button>
                    </div>
                </div>
            </ng-template>
            <ava-approval-card height="300" [contentTemplate]="footerTemplate" [cardData]="consoleApproval"
            [contentsBackground]="'#ffffff'" [cardContainerBackground]="'#F8F8F8'">
            </ava-approval-card>
        </div>
    </div>
</div>