.approval {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
}

.approval-left-screen {
  flex: 0 0 280px;           /* Fixed width to match your ava-sidebar width */
  max-width: 280px;
  min-width: 65px;           /* To allow collapsedWidth */
  background-color: #f5f5f5; /* optional background color */
  height: 120vh;             /* Make it full height if needed */
  overflow: hidden;
}

/* Right side (main content) */
.approval-right-screen {
  flex: 1;                   /* Take remaining space */
  padding: 1rem;             /* Some padding for content */
  overflow-y: auto;          /* Scrollable if content is long */
  background-color: #ffffff; /* optional */
}

/* Container stacking */
.approval-title-filter {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem; /* gap below entire header section */
}

/* Title styling */
.approvals-title {
  font-weight: bold;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

/* Filter section layout */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;            /* left and right padding */
  margin-bottom: 1rem;        /* space below filter section */
}

/* Search bars (left) */
.search-bars {
  display: flex;
  gap: 1rem;                  /* space between dropdowns and button */
}

/* Textbox section (right) */
.textbox.section {
  margin-left: 1rem;
}

.approval-card-header {
  font-size: 1.25rem;              /* Slightly larger text */
  font-weight: 600;                /* Semi-bold */
  color: #333;                     /* Dark gray text */
  padding: 0.75rem 1rem;           /* Space inside the header */
  margin-bottom: 1rem;             /* Space below the header */
  display: flex;
  align-items: center;
  justify-content: space-between;  /* In case you later add actions on the right */
}


.approval-title-filter {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: nowrap;
  width: 100%;
  padding: 1rem 0;
}

.approval-title-filter > ava-text-card {
  flex: 1 1 22%;
  min-width: 200px;
}

.quick-actions-content {
  padding: 20px 16px;
  overflow-y: auto;
  flex-grow: 1;
  
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 16px; /* Increased gap for better spacing */
    
    .action-button {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 16px; /* Wider gap for better spacing */
      padding: 16px 20px; /* More padding for better touch area */
      border-radius: 12px; /* Rounded corners */
      border: none;
      background: linear-gradient(118deg, #7FC2EB 0%, #5B92EA 89.27%); /* Use the new variable */
      cursor: pointer;
      transition: all var(--transition-speed) ease;
      width: 100%;
      text-align: left;
      
      &:hover {
        opacity: 0.9;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--dashboard-shadow-hover);
      }
      
      .action-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        
        img {
          width: 20px;
          height: 20px;
          filter: brightness(0) invert(1); /* Make SVG white */
        }
      }
      
      .action-label {
        font-size: 12px;
        font-weight: 300;
        color: var(--dashboard-quick-action-text); /* Use the new variable */
      }
    }
  }
}


/* Responsive (optional) for mobile screens */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }

  .search-bars,
  .textbox.section {
    width: 100%;
    margin: 0 0 0.5rem 0;
    justify-content: center;
  }

  .search-bars {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

