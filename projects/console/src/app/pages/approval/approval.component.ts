import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { CommonModule } from "@angular/common";
import { RouterModule } from '@angular/router';
import { Router } from '@angular/router';
import { ApprovalCardComponent, AvaTextboxComponent, DropdownComponent, DropdownOption, IconComponent, SidebarComponent, TextCardComponent } from '@ava/play-comp-library';
import { ButtonComponent } from '@ava/play-comp-library';
import { SharedApiServiceService } from '../../shared/services/shared-api-service.service';
import { CardsComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-approval',
  standalone: true,
  imports: [CommonModule, RouterModule, CardsComponent, ApprovalCardComponent, ButtonComponent, IconComponent, DropdownComponent, AvaTextboxComponent, TextCardComponent, SidebarComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './approval.component.html',
  styleUrl: './approval.component.scss'
})
export class ApprovalComponent implements OnInit {

  public searchValue : string = '';
  public totalAgentsApprovals : number = 60;
  public totalHighPriorityApprovals : number = 15;
  public totalLowPriorityApprovals : number = 20;
  public totalMidPriorityApprovals : number = 25;
  public isBasicCollapsed : boolean = false;
  public quickActionsExpanded: boolean = true;
  public consoleApproval = {
    contents: [
    {
      session1: {
        title: 'Autonomous Systems.... Agent1',
        labels: [
          {
            name: 'Agent',
            color: 'success',
            background: 'red',
            type: 'normal'
          },
          {
            name: 'High',
            color: 'error',
            background: 'red',
            type: 'pill'
          }
        ]
      },
      session2: [
        {
          name: 'Agent',
          color: 'default',
          background: 'red',
          type: 'normal'
        },
        {
          name: 'High',
          color: 'default',
          background: 'red',
          type: 'normal'
        }
      ],
      session3: [
        {
          iconName: 'user',
          label: '<EMAIL>',
        },
        {
          iconName: 'calendar-days',
          label: '12 May 2025',
        },
      ],
      session4:
      {
        status: 'Execution was successful',
        iconName: 'circle-check-big',
      },

    },
    {
      session1: {
        title: 'Autonomous Systems.... Agent1',
        labels: [
          {
            name: 'Agent',
            color: 'success',
            background: 'red',
            type: 'normal'
          },
          {
            name: 'High',
            color: 'error',
            background: 'red',
            type: 'pill'
          }
        ]
      },
      session2: [
        {
          name: 'Agent',
          color: 'default',
          background: 'red',
          type: 'normal'
        },
        {
          name: 'High',
          color: 'default',
          background: 'red',
          type: 'normal'
        }
      ],
      session3: [
        {
          iconName: 'user',
          label: '<EMAIL>',
        },
        {
          iconName: 'calendar-days',
          label: '12 May 2025',
        },
      ],
      session4:
      {
        status: 'Execution was successful',
        iconName: 'circle-check-big',
      },
    },
    {
      session1: {
        title: 'Autonomous Systems.... Agent1',
        labels: [
          {
            name: 'Agent',
            color: 'success',
            background: 'red',
            type: 'normal'
          },
          {
            name: 'High',
            color: 'error',
            background: 'red',
            type: 'pill'
          }
        ]
      },
      session2: [
        {
          name: 'Agent',
          color: 'default',
          background: 'red',
          type: 'normal'
        },
        {
          name: 'High',
          color: 'default',
          background: 'red',
          type: 'normal'
        }
      ],
      session3: [
        {
          iconName: 'user',
          label: '<EMAIL>',
        },
        {
          iconName: 'calendar-days',
          label: '12 May 2025',
        },
      ],
      session4:
      {
        status: 'Execution was successful',
        iconName: 'circle-check-big',
      },
    }
    ],
    footer: {},
  }

  public options: DropdownOption[] = [
    { name: 'Electronics', value: 'electronics' },
    { name: 'Clothing', value: 'clothing' },
    { name: 'Books', value: 'books' }
  ];

   public basicSidebarItems: any[] = [
    { id: '1', icon: 'hammer', text: 'Agents', route: '', active: true },
    { id: '2', icon: 'circle-check', text: 'Workflows', route: '' },
    { id: '3', icon: 'bot', text: 'Tools', route: '' },
  ];

  quickActions: any[] = [
    {
      icon: 'awe_agents',
      label: 'Agents',
      route: ''
    },
    {
      icon: 'awe_workflows',
      label: 'Workflows',
      route: ''
    },
    {
      icon: 'awe_tools',
      label: 'Tools',
      route: ''
    },
  ];

 
  constructor(private router: Router, private apiService: SharedApiServiceService) {}

  ngOnInit(): void {
    this.totalAgentsApprovals = 60;
  }
  
  public onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
  
  public uClick(i: any) {
    console.log("log" + i);
  }

  public onBasicCollapseToggle(isCollapsed: boolean): void {
    this.isBasicCollapsed = isCollapsed;
    console.log('Basic sidebar collapsed:', isCollapsed);
  }


  public onBasicItemClick(item: any): void {
    this.basicSidebarItems.forEach((i) => (i.active = false));
    item.active = true;
    console.log(item);
  }
} 