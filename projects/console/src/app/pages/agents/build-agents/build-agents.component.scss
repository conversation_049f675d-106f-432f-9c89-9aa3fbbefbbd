.build-agents-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  overflow: hidden;

  .header-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    min-height: 60px;

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .nav-item {
        font-size: 14px;
        color: var(--text-secondary);
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: var(--text-primary);
        }

        &.active {
          color: var(--text-primary);
          font-weight: 500;
        }
      }

      .separator {
        color: var(--text-tertiary);
        font-size: 14px;
      }

      .close-btn {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        margin-left: 16px;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--hover-bg);
          color: var(--text-primary);
        }
      }
    }

    .header-actions {
      .action-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-btn {
          background: none;
          border: 1px solid var(--border-color);
          color: var(--text-secondary);
          cursor: pointer;
          padding: 8px;
          border-radius: 6px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background-color: var(--hover-bg);
            border-color: var(--border-hover);
            color: var(--text-primary);
          }
        }

        .run-btn {
          background: var(--dashboard-primary);
          border: 1px solid var(--dashboard-primary);
          color: white;
          cursor: pointer;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 6px;
          transition: all 0.2s ease;

          &:hover {
            background: var(--dashboard-primary-hover);
            border-color: var(--dashboard-primary-hover);
          }
        }
      }
    }
  }

  .main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    height: calc(100vh - 100px); // Fixed height: full viewport minus header and some padding
    max-height: 700px; // Maximum height to prevent too tall on large screens

    .left-sidebar {
      width: 320px;
      background-color: var(--card-bg);
      border-right: 1px solid var(--border-color);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      height: 100%; // Take full height of main-content
      max-height: 800px; // Maximum height to prevent too tall

      .sidebar-header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid var(--border-color);

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--text-primary);
        }
      }

      .tools-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .tabs-container {
          padding: 16px 24px 8px;
          border-bottom: 1px solid var(--border-color);

          .custom-tabs {
            display: flex;
            flex-direction: row;
            gap: 8px;
            justify-content: space-between;

            .tab-button {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: center;
              height: 48px;
              background-color: transparent;
              border: 2px solid transparent;
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.15s ease;
              padding: 8px;
              min-width: 48px;

              &:hover {
                background-color: var(--hover-bg);
                border-color: var(--border-hover);
              }

              &.active {
                background-color: var(--dashboard-primary);
                border-color: var(--dashboard-primary);

                .tab-icon {
                  filter: brightness(0) invert(1); // Make icon white when active
                }
              }

              .tab-icon {
                width: 24px;
                height: 24px;
                object-fit: contain;
                transition: filter 0.15s ease;
              }
            }
          }
        }

        .search-container {
          padding: 16px 24px;
          border-bottom: 1px solid var(--border-color);

          .search-input-wrapper {
            position: relative;

            .search-icon {
              position: absolute;
              left: 12px;
              top: 50%;
              transform: translateY(-50%);
              color: var(--text-tertiary);
              pointer-events: none;
            }

            .search-input {
              width: 100%;
              padding: 10px 12px 10px 40px;
              border: 1px solid var(--border-color);
              border-radius: 6px;
              background-color: var(--input-bg);
              color: var(--text-primary);
              font-size: 14px;
              transition: all 0.2s ease;

              &:focus {
                outline: none;
                border-color: var(--dashboard-primary);
                box-shadow: 0 0 0 2px var(--dashboard-shadow-hover);
              }

              &::placeholder {
                color: var(--text-tertiary);
              }
            }
          }
        }

        .tools-list {
          flex: 1;
          overflow-y: auto;
          padding: 8px 0;

          .tool-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 16px 24px;
            cursor: grab;
            transition: all 0.2s ease;
            border-bottom: 1px solid var(--border-light);

            &:hover {
              background-color: var(--hover-bg);
            }

            &:active {
              cursor: grabbing;
            }

            .tool-icon {
              flex-shrink: 0;
              width: 40px;
              height: 40px;
              background-color: var(--icon-bg);
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: var(--dashboard-primary);
            }

            .tool-content {
              flex: 1;
              min-width: 0;

              .tool-name {
                margin: 0 0 4px 0;
                font-size: 14px;
                font-weight: 500;
                color: var(--text-primary);
              }

              .tool-description {
                margin: 0;
                font-size: 12px;
                color: var(--text-secondary);
                line-height: 1.4;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }
          }
        }

        .create-tool-section {
          padding: 16px 24px;
          border-top: 1px solid var(--border-color);

          .create-tool-btn {
            width: 100%;
            background: var(--dashboard-primary);
            border: none;
            color: white;
            cursor: pointer;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.2s ease;

            &:hover {
              background: var(--dashboard-primary-hover);
            }
          }
        }
      }
    }

    .canvas-area {
      flex: 1;
      position: relative;
      background-color: var(--background-secondary);
      overflow: hidden;
      height: 100%; // Take full height of main-content
      max-height: 800px; // Maximum height to prevent too tall

      ::ng-deep app-canvas-board {
        height: 100%;
        width: 100%;

        .canvas-container {
          height: 100%;
          background-color: var(--background-secondary);
        }

        .canvas-viewport {
          height: 100%;
        }

        .fallback-message {
          color: var(--text-tertiary);
          font-size: 16px;
          text-align: center;
          padding: 40px;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .build-agents-container {
    .main-content {
      .left-sidebar {
        width: 280px;
      }
    }
  }
}

@media (max-width: 768px) {
  .build-agents-container {
    .header-nav {
      padding: 8px 16px;

      .breadcrumb {
        .nav-item {
          font-size: 13px;
        }
      }

      .header-actions {
        .action-group {
          gap: 4px;

          .action-btn {
            padding: 6px;
          }

          .run-btn {
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }
    }

    .main-content {
      .left-sidebar {
        width: 260px;

        .tools-section {
          .tabs-container {
            padding: 12px 16px 8px;

            ::ng-deep ava-tabs {
              .tab-list {
                gap: 4px;
              }

              .tab-item {
                padding: 10px 6px;
                min-height: 44px;

                i {
                  font-size: 16px;
                }
              }
            }
          }

          .search-container {
            padding: 12px 16px;
          }

          .tools-list {
            .tool-item {
              padding: 12px 16px;
            }
          }

          .create-tool-section {
            padding: 12px 16px;
          }
        }
      }
    }
  }
}
