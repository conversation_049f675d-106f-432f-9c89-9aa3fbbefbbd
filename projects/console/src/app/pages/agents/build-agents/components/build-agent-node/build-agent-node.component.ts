import { Component, Input, Output, EventEmitter, ElementRef, ViewChild, AfterViewInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule, CdkDragEnd, CdkDrag } from '@angular/cdk/drag-drop';

export interface BuildAgentNodeData {
  id: string;
  type: 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail';
  name: string;
  icon?: string;
  position: { x: number; y: number };
}

@Component({
  selector: 'app-build-agent-node',
  standalone: true,
  imports: [CommonModule, DragDropModule],
  templateUrl: './build-agent-node.component.html',
  styleUrls: ['./build-agent-node.component.scss']
})
export class BuildAgentNodeComponent implements AfterViewInit, OnChanges {
  @ViewChild('nodeElement') nodeElement!: ElementRef;
  @ViewChild('dragRef') cdkDrag!: CdkDrag;

  @Input() node: any;
  @Input() selected: boolean = false;
  @Input() mouseInteractionsEnabled: boolean = true;
  @Input() canvasMode: string = 'select';

  @Output() deleteNode = new EventEmitter<string>();
  @Output() moveNode = new EventEmitter<{nodeId: string, position: {x: number, y: number}}>();
  @Output() nodeSelected = new EventEmitter<string>();
  @Output() startConnection = new EventEmitter<{nodeId: string, handleType: 'top' | 'right' | 'bottom' | 'left', event: MouseEvent}>();
  @Output() nodePositionChanged = new EventEmitter<{nodeId: string, position: {x: number, y: number}}>();

  isDragging: boolean = false;
  readonly nodeWidth: number = 160;

  private readonly nodeConfig = {
    prompt: { icon: 'assets/images/prompt.png', bgColor: '#fef3c7', borderColor: '#f59e0b', textColor: '#92400e' },
    model: { icon: 'assets/images/Boundingbox.png', bgColor: '#dbeafe', borderColor: '#3b82f6', textColor: '#1e40af' },
    knowledge: { icon: 'assets/images/imprort_contacts.png', bgColor: '#dcfce7', borderColor: '#10b981', textColor: '#065f46' },
    tool: { icon: 'assets/images/build.png', bgColor: '#fce7f3', borderColor: '#ec4899', textColor: '#be185d' },
    guardrail: { icon: 'assets/images/swords.png', bgColor: '#f3e8ff', borderColor: '#8b5cf6', textColor: '#5b21b6' }
  };

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['node']?.currentValue) {
      const currentNode = changes['node'].currentValue;
      const previousNode = changes['node'].previousValue;

      if (this.hasPositionChanged(currentNode, previousNode)) {
        this.updateCdkDragPosition(currentNode.data.position);
      }
    }
  }

  private hasPositionChanged(currentNode: any, previousNode: any): boolean {
    if (!currentNode?.data?.position) return false;
    if (!previousNode?.data?.position) return true;

    const current = currentNode.data.position;
    const previous = previousNode.data.position;
    return current.x !== previous.x || current.y !== previous.y;
  }

  ngAfterViewInit(): void {
    const nodeData = this.currentNodeData;
    if (nodeData) {
      setTimeout(() => this.nodePositionChanged.emit({
        nodeId: nodeData.id,
        position: nodeData.position
      }), 0);
    }
  }

  private updateCdkDragPosition(position: { x: number; y: number }): void {
    if (this.isDragging) return;

    setTimeout(() => {
      try {
        if (this.cdkDrag) {
          this.cdkDrag.reset();
          this.cdkDrag.setFreeDragPosition(position);
        }

        if (this.nodeElement?.nativeElement) {
          this.nodeElement.nativeElement.style.transform = `translate3d(${position.x}px, ${position.y}px, 0px)`;
        }
      } catch (error) {
        console.warn('Error updating CDK drag position:', error);
        if (this.nodeElement?.nativeElement) {
          const element = this.nodeElement.nativeElement;
          element.style.position = 'absolute';
          element.style.left = `${position.x}px`;
          element.style.top = `${position.y}px`;
          element.style.transform = '';
        }
      }
    }, 50);
  }

  get currentNodeData(): BuildAgentNodeData {
    return this.node?.data || {};
  }

  get isCurrentlySelected(): boolean {
    return this.selected;
  }

  get currentConfig() {
    const nodeData = this.currentNodeData;
    return this.nodeConfig[nodeData.type] || this.nodeConfig.prompt;
  }

  onNodeClick(): void {
    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {
      this.nodeSelected.emit(this.currentNodeData.id);
    }
  }

  onNodeDoubleClick(): void {
    // Reserved for future functionality
  }

  onDeleteClick(event: Event): void {
    event.stopPropagation();
    if (this.currentNodeData.id) {
      this.deleteNode.emit(this.currentNodeData.id);
    }
  }

  onDragEnded(event: CdkDragEnd): void {
    if (!this.mouseInteractionsEnabled) return;

    this.isDragging = false;
    const nodeData = this.currentNodeData;
    if (!nodeData?.id) return;

    const transform = event.source.getFreeDragPosition();
    const safePosition = {
      x: Math.max(0, transform.x),
      y: Math.max(0, transform.y)
    };

    this.moveNode.emit({ nodeId: nodeData.id, position: safePosition });
    this.nodePositionChanged.emit({ nodeId: nodeData.id, position: safePosition });
  }

  onNodeMouseDown(event: MouseEvent): void {
    if (this.mouseInteractionsEnabled) {
      this.isDragging = true;
      this.onNodeClick();
    }
  }

  onHandleMouseDown(event: MouseEvent, handleType: 'top' | 'right' | 'bottom' | 'left'): void {
    if (!this.mouseInteractionsEnabled) return;

    event.stopPropagation();
    const nodeData = this.currentNodeData;
    if (!nodeData?.id) return;

    this.nodeSelected.emit(nodeData.id);
    this.nodePositionChanged.emit({ nodeId: nodeData.id, position: nodeData.position });
    this.startConnection.emit({ nodeId: nodeData.id, handleType, event });
  }
}
