<div
  #nodeElement
  class="build-agent-node"
  [class.selected]="isCurrentlySelected"
  [class.dragging]="isDragging"
  [class.disabled]="!mouseInteractionsEnabled"
  [attr.data-node-id]="currentNodeData?.id"

  cdkDrag
  #dragRef="cdkDrag"
  [cdkDragDisabled]="!mouseInteractionsEnabled"
  cdkDragBoundary=".canvas-container"
  [cdkDragFreeDragPosition]="{x: currentNodeData?.position?.x || 0, y: currentNodeData?.position?.y || 0}"
  (cdkDragEnded)="onDragEnded($event)"
  (mousedown)="onNodeMouseDown($event)"
  (click)="onNodeClick()"
  (dblclick)="onNodeDoubleClick()">

  <!-- Node Icon -->
  <div class="node-icon">
    <img [src]="currentNodeData?.icon || currentConfig.icon" [alt]="currentNodeData?.type" />
  </div>

  <!-- Node Name -->
  <div class="node-name">
    {{ currentNodeData?.name }}
  </div>

  <!-- Delete <PERSON><PERSON> (only show on hover/selected) -->
  <button
    class="delete-btn"
    (click)="onDeleteClick($event)"
    title="Delete node">
    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
  </button>

  <!-- Connection Points - 4 sides -->
  <div
    class="connection-point top"
    data-handle="top"
    (mousedown)="onHandleMouseDown($event, 'top')">
  </div>
  <div
    class="connection-point right"
    data-handle="right"
    (mousedown)="onHandleMouseDown($event, 'right')">
  </div>
  <div
    class="connection-point bottom"
    data-handle="bottom"
    (mousedown)="onHandleMouseDown($event, 'bottom')">
  </div>
  <div
    class="connection-point left"
    data-handle="left"
    (mousedown)="onHandleMouseDown($event, 'left')">
  </div>
</div>
