.build-agent-node {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  min-width: 120px;
  max-width: 180px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 20px; // More pill-like
  cursor: pointer;
  user-select: none;
  transition: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 10;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
    z-index: 20;

    .delete-btn {
      opacity: 1;
      pointer-events: auto;
    }
  }

  &.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
    z-index: 30;

    .delete-btn {
      opacity: 1;
      pointer-events: auto;
    }
  }

  &.dragging {
    z-index: 40;
    transform: rotate(2deg) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed !important;
    pointer-events: none;

    &:hover {
      transform: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      border-color: #e5e7eb;
    }
  }

  .node-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 16px;
      height: 16px;
      object-fit: contain;
    }
  }

  .node-name {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #374151;
  }

  .delete-btn {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 18px;
    height: 18px;
    background: #ef4444;
    border: 2px solid white;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: none;

    &:hover {
      background: #dc2626;
      transform: scale(1.1);
    }

    svg {
      width: 8px;
      height: 8px;
    }
  }

  // Connection points - 4 sides
  .connection-point {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #3b82f6;
    border: 2px solid white;
    border-radius: 50%;
    opacity: 0;
    transition: all 0.2s ease;
    z-index: 15;
    cursor: crosshair;

    &:hover {
      background: #2563eb;
      transform: scale(1.3);
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
    }

    &.top {
      top: -6px;
      left: 50%;
      transform: translateX(-50%);

      &:hover {
        transform: translateX(-50%) scale(1.3);
      }
    }

    &.right {
      right: -6px;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        transform: translateY(-50%) scale(1.3);
      }
    }

    &.bottom {
      bottom: -6px;
      left: 50%;
      transform: translateX(-50%);

      &:hover {
        transform: translateX(-50%) scale(1.3);
      }
    }

    &.left {
      left: -6px;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        transform: translateY(-50%) scale(1.3);
      }
    }
  }

  // Show connection points on hover and when selected
  &:hover .connection-point,
  &.selected .connection-point {
    opacity: 1;
  }
}

// Animation for pulse effect when connecting
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
