import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ButtonComponent } from '@ava/play-comp-library';
import { CanvasBoardComponent, CanvasNode, CanvasEdge } from '../../../shared/components/canvas-board/canvas-board.component';
import { BuildAgentNodeComponent, BuildAgentNodeData } from './components/build-agent-node';
import { SelectOption } from '../../../shared/components/select-dropdown/select-dropdown.component';
import {PlaygroundComponent} from '../../../shared/components/playground/playground.component';

interface ToolItem {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'tool' | 'model' | 'knowledge' | 'prompt' | 'guardrail';
}

interface AvaTab {
  label: string;
  value: string;
  icon: string;
}

@Component({
  selector: 'app-build-agents',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonComponent,
    CanvasBoardComponent,
    BuildAgentNodeComponent,
    PlaygroundComponent
  ],
  templateUrl: './build-agents.component.html',
  styleUrls: ['./build-agents.component.scss']
})
export class BuildAgentsComponent implements OnInit {
  searchQuery: string = '';
  activeTab: string = 'prompts';
  canvasNodes: CanvasNode[] = [];
  canvasEdges: CanvasEdge[] = [];
  buildAgentNodes: BuildAgentNodeData[] = [];
  selectedNodeId: string | null = null;

  private readonly nodeTypeOrder: ('prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail')[] = [
    'prompt', 'model', 'knowledge', 'tool', 'guardrail'
  ];

  readonly tabs: AvaTab[] = [
    { label: 'Prompts', value: 'prompts', icon: 'assets/images/prompt.png' },
    { label: 'Models', value: 'models', icon: 'assets/images/deployed_code.png' },
    { label: 'Knowledge Base', value: 'knowledge', icon: 'assets/images/import_contacts.png' },
    { label: 'Tools', value: 'tools', icon: 'assets/images/build.png' },
    { label: 'Guardrails', value: 'guardrails', icon: 'assets/images/swords.png' }
  ];

  readonly agentTypeOptions: SelectOption[] = [
    { value: 'individual', label: 'Individual Agent' },
    { value: 'collaborative', label: 'Collaborative Agent' }
  ];

  private readonly allToolItems: { [key: string]: ToolItem[] } = {
    prompts: this.generateMockItems('prompt', 'Prompt Name', 'assets/images/prompt.png', 5),
    models: this.generateMockItems('model', 'Model Name', 'assets/images/deployed_code.png', 2),
    knowledge: this.generateMockItems('knowledge', 'Knowledge Base Name', 'assets/images/import_contacts.png', 2),
    tools: this.generateMockItems('tool', 'Tool Name', 'assets/images/build.png', 2),
    guardrails: this.generateMockItems('guardrail', 'Guardrail Name', 'assets/images/swords.png', 2)
  };

  // Canvas configuration
  navigationHints: string[] = [
    'Alt + Drag to pan canvas',
    'Mouse wheel to zoom',
    'Space to reset view'
  ];

  constructor(private router: Router, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.activeTab = 'prompts';
  }

  private generateMockItems(type: string, name: string, icon: string, count: number): ToolItem[] {
    return Array.from({ length: count }, (_, i) => ({
      id: `${type}${i + 1}`,
      name,
      description: 'AI agents are software programs that use artificial intelligence to perform tasks and achieve goals.',
      icon,
      type: type as any
    }));
  }

  get currentTabTools(): ToolItem[] {
    return this.allToolItems[this.activeTab] || [];
  }

  get filteredTools(): ToolItem[] {
    const currentTools = this.currentTabTools;
    if (!this.searchQuery) return currentTools;

    const query = this.searchQuery.toLowerCase();
    return currentTools.filter(tool =>
      tool.name.toLowerCase().includes(query) ||
      tool.description.toLowerCase().includes(query)
    );
  }

  onTabChange(tabValue: string): void {
    this.activeTab = tabValue;
    this.searchQuery = '';
  }

  onDragStart(event: DragEvent, tool: ToolItem): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/reactflow', JSON.stringify(tool));
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onCanvasDropped(event: {event: DragEvent, position: {x: number, y: number}}): void {
    const toolData = event.event.dataTransfer?.getData('application/reactflow');
    if (!toolData) return;

    try {
      const tool = JSON.parse(toolData);
      const autoPosition = this.calculateAutoPosition(tool.type);

      const buildAgentNode: BuildAgentNodeData = {
        id: this.generateNodeId(),
        name: tool.name,
        icon: tool.icon,
        type: tool.type,
        position: autoPosition
      };

      const newCanvasNode: CanvasNode = {
        id: buildAgentNode.id,
        type: 'build-agent',
        data: { ...buildAgentNode, width: 160 },
        position: autoPosition
      };

      this.buildAgentNodes = [...this.buildAgentNodes, buildAgentNode];
      this.canvasNodes = [...this.canvasNodes, newCanvasNode];
      this.createAutomaticConnections(buildAgentNode);
    } catch (error) {
      console.error('Error adding node:', error);
    }
  }

  onNodeSelected(nodeId: string): void {
    this.selectedNodeId = nodeId;
  }

  onNodeMoved(event: {nodeId: string, position: {x: number, y: number}}): void {
    this.updateNodePosition(this.buildAgentNodes, event.nodeId, event.position);
    this.updateNodePosition(this.canvasNodes, event.nodeId, event.position);
  }

  onDeleteNode(nodeId: string): void {
    this.buildAgentNodes = this.buildAgentNodes.filter(node => node.id !== nodeId);
    this.canvasNodes = this.canvasNodes.filter(node => node.id !== nodeId);
    this.canvasEdges = this.canvasEdges.filter(edge =>
      edge.source !== nodeId && edge.target !== nodeId
    );
  }

  private updateNodePosition(nodes: any[], nodeId: string, position: {x: number, y: number}): void {
    const nodeIndex = nodes.findIndex(node => node.id === nodeId);
    if (nodeIndex !== -1) {
      nodes[nodeIndex].position = position;
    }
  }



  onConnectionCreated(edge: CanvasEdge): void {
    // Create a proper edge with unique ID
    const newEdge: CanvasEdge = {
      id: edge.id || `edge_${edge.source}_${edge.target}_${Math.floor(Math.random() * 1000)}`,
      source: edge.source,
      target: edge.target,
      animated: edge.animated || true
    };

    this.canvasEdges = [...this.canvasEdges, newEdge];
    console.log('Connection created:', newEdge);
  }

  onStartConnection(event: {nodeId: string, handleType: 'source' | 'target', event: MouseEvent}): void {
    // Canvas board handles connection logic
    // This method is called when a connection starts but the canvas board manages the temp connection
    console.log('Connection started:', event);
  }

  // Handle node position changes for connection point updates
  onNodePositionChanged(event: {nodeId: string, position: {x: number, y: number}}): void {
    // Update build agent nodes
    const buildAgentNodeIndex = this.buildAgentNodes.findIndex(node => node.id === event.nodeId);
    if (buildAgentNodeIndex !== -1) {
      this.buildAgentNodes[buildAgentNodeIndex].position = event.position;
    }

    // Update canvas nodes
    const canvasNodeIndex = this.canvasNodes.findIndex(node => node.id === event.nodeId);
    if (canvasNodeIndex !== -1) {
      this.canvasNodes[canvasNodeIndex].position = event.position;
    }
  }

  // Toolbar actions
  onUndo(): void {
    // Implement undo functionality
  }

  onRedo(): void {
    // Implement redo functionality
  }

  onReset(): void {
    // Clear all nodes and edges
    this.buildAgentNodes = [];
    this.canvasNodes = [];
    this.canvasEdges = [];
  }

  onRun(): void {
    // Implement run/execute functionality
    console.log('Running agent configuration...');
  }

  onCanvasStateChanged(state: {nodes: CanvasNode[], edges: CanvasEdge[]}): void {
    this.canvasNodes = state.nodes;
    this.canvasEdges = state.edges;

    // Sync build agent nodes with canvas nodes
    this.buildAgentNodes = state.nodes.map(canvasNode => ({
      id: canvasNode.id,
      name: canvasNode.data.name || canvasNode.data.label,
      type: canvasNode.data.type,
      icon: canvasNode.data.icon,
      position: canvasNode.position
    }));
  }

  // Get active tab label for display
  getActiveTabLabel(): string {
    const activeTabObj = this.tabs.find(tab => tab.value === this.activeTab);
    return activeTabObj ? activeTabObj.label : 'Item';
  }

  // Create new item action (dynamic based on active tab)
  onCreateNewItem(): void {
    const tabLabel = this.getActiveTabLabel();
    console.log(`Creating new ${tabLabel}...`);
    // Navigate to appropriate creation page based on active tab
    // You can add routing logic here based on the active tab
  }

  // Built-in canvas board event handlers
  onAgentNameChanged(name: string): void {
    console.log('Agent name changed:', name);
    // Handle agent name change
  }

  onAgentTypeChanged(type: string): void {
    console.log('Agent type changed:', type);
    // Handle agent type change
  }

  onMetadataChanged(metadata: {org: string, domain: string, project: string, team: string}): void {
    console.log('Metadata changed:', metadata);
    // Handle metadata change
  }





  // Calculate automatic position based on node type and existing nodes
  private calculateAutoPosition(nodeType: string): {x: number, y: number} {
    const typeIndex = this.nodeTypeOrder.indexOf(nodeType as any);
    const baseX = 100;
    const baseY = 100;
    const horizontalSpacing = 250;
    const verticalSpacing = 150;

    // Check if this node type already exists
    const existingNodeOfType = this.buildAgentNodes.find(node => node.type === nodeType);
    if (existingNodeOfType) {
      // If node type exists, position it slightly offset
      return {
        x: existingNodeOfType.position.x + 50,
        y: existingNodeOfType.position.y + 50
      };
    }

    // Calculate position based on workflow: prompt → model → knowledge → tool → guardrail
    if (typeIndex === -1) {
      // Unknown type, place at end
      return { x: baseX + (this.nodeTypeOrder.length * horizontalSpacing), y: baseY };
    }

    // Position based on the workflow sequence
    switch (typeIndex) {
      case 0: // prompt
        return { x: baseX, y: baseY };
      case 1: // model
        return { x: baseX + horizontalSpacing, y: baseY };
      case 2: // knowledge
        return { x: baseX + (horizontalSpacing / 2), y: baseY + verticalSpacing };
      case 3: // tool
        return { x: baseX + (horizontalSpacing * 1.5), y: baseY + verticalSpacing };
      case 4: // guardrail
        return { x: baseX + horizontalSpacing, y: baseY + (verticalSpacing * 2) };
      default:
        return { x: baseX + (typeIndex * horizontalSpacing), y: baseY };
    }
  }

  // Create automatic connections based on workflow logic
  private createAutomaticConnections(newNode: BuildAgentNodeData): void {
    const typeIndex = this.nodeTypeOrder.indexOf(newNode.type);

    // Define connection rules based on the workflow image
    const connectionRules: {[key: string]: string[]} = {
      'prompt': ['model'], // prompt connects to model
      'model': ['knowledge'], // model connects to knowledge
      'knowledge': ['tool'], // knowledge connects to tool
      'tool': ['guardrail'], // tool connects to guardrail
      'guardrail': [] // guardrail is the end
    };

    // Connect to previous nodes (incoming connections)
    for (const existingNode of this.buildAgentNodes) {
      if (existingNode.id === newNode.id) continue;

      const existingRules = connectionRules[existingNode.type] || [];
      if (existingRules.includes(newNode.type)) {
        this.createConnection(existingNode.id, newNode.id);
      }
    }

    // Connect to next nodes (outgoing connections)
    const outgoingRules = connectionRules[newNode.type] || [];
    for (const targetType of outgoingRules) {
      const targetNode = this.buildAgentNodes.find(node => node.type === targetType);
      if (targetNode) {
        this.createConnection(newNode.id, targetNode.id);
      }
    }
  }

  // Helper method to create a connection between two nodes
  private createConnection(sourceId: string, targetId: string): void {
    // Check if connection already exists
    const existingConnection = this.canvasEdges.find(
      edge => (edge.source === sourceId && edge.target === targetId) ||
              (edge.source === targetId && edge.target === sourceId)
    );

    if (existingConnection) {
      return; // Connection already exists
    }

    const newEdge: CanvasEdge = {
      id: `edge_${sourceId}_${targetId}_${Math.floor(Math.random() * 1000)}`,
      source: sourceId,
      target: targetId,
      animated: true
    };

    this.canvasEdges = [...this.canvasEdges, newEdge];
    console.log('Auto-connection created:', newEdge);

    // Recalculate positions after connection is created
    this.recalculateNodePositions();
  }

  // Recalculate and update node positions based on connections
  private recalculateNodePositions(): void {
    let positionsChanged = false;

    // Update positions in place to maintain object references
    for (let i = 0; i < this.buildAgentNodes.length; i++) {
      const node = this.buildAgentNodes[i];
      const newPosition = this.calculateAutoPosition(node.type);

      // Only update if position actually changed
      if (node.position.x !== newPosition.x || node.position.y !== newPosition.y) {
        this.buildAgentNodes[i].position = newPosition;
        positionsChanged = true;
      }
    }

    // Update canvas nodes to match if positions changed
    if (positionsChanged) {
      // Update canvas nodes in place to maintain references
      for (let i = 0; i < this.canvasNodes.length; i++) {
        const canvasNode = this.canvasNodes[i];
        const buildAgentNode = this.buildAgentNodes.find(n => n.id === canvasNode.id);
        if (buildAgentNode) {
          this.canvasNodes[i].position = buildAgentNode.position;
          // Also update the data position
          this.canvasNodes[i].data.position = buildAgentNode.position;
        }
      }

      // Force change detection to trigger ngOnChanges in child components
      setTimeout(() => {
        // Create new array references to trigger change detection
        this.canvasNodes = [...this.canvasNodes];
        this.buildAgentNodes = [...this.buildAgentNodes];
      }, 0);
    }
  }

  // Generate a unique node ID - similar to workflow editor
  generateNodeId(): string {
    return `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }

  // Navigation
  goBack(): void {
    this.router.navigate(['/agents']);
  }
}
