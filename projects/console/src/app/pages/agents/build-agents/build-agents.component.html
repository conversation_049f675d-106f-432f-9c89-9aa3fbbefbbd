<div class="build-agents-container">
  <!-- Header Navigation -->
  <div class="header-nav">

  <!-- Main Content Area -->
  <div class="main-content">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
      <div class="sidebar-header">
        <h3>Configure Agent</h3>
      </div>

      <!-- Tool Items Section -->
      <div class="tools-section">
        <!-- Custom Icon-Only Tabs -->
        <div class="tabs-container">
          <div class="custom-tabs">
            <button
              *ngFor="let tab of tabs"
              class="tab-button"
              [class.active]="activeTab === tab.value"
              (click)="onTabChange(tab.value)"
              [title]="tab.label">
              <img [src]="tab.icon" [alt]="tab.label" class="tab-icon">
            </button>
          </div>
        </div>

        <!-- Search Bar -->
        <div class="search-container">
          <div class="search-input-wrapper">
            <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <input
              type="text"
              placeholder="Search {{ activeTab }}"
              [(ngModel)]="searchQuery"
              class="search-input">
          </div>
        </div>

        <!-- Tool Items List -->
        <div class="tools-list">
          <div
            *ngFor="let tool of filteredTools"
            class="tool-item"
            draggable="true"
            (dragstart)="onDragStart($event, tool)">

            <div class="tool-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
              </svg>
            </div>

            <div class="tool-content">
              <h4 class="tool-name">{{ tool.name }}</h4>
              <p class="tool-description">{{ tool.description }}</p>
            </div>
          </div>
        </div>

        <!-- Create New Item Button -->
        <div class="create-tool-section">
          <button class="create-tool-btn" (click)="onCreateNewItem()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            + Create New {{ getActiveTabLabel() }}
          </button>
        </div>
      </div>
    </div>

    <!-- Canvas Area -->
    <div class="canvas-area">
      <app-canvas-board
        [nodes]="canvasNodes"
        [edges]="canvasEdges"
        [navigationHints]="[]"
        [fallbackMessage]="'Drag tools from the left panel to build your agent'"
        [primaryButtonText]="'Execute Agent'"
        [showToolbar]="true"
        [enableConnections]="true"
        [enablePan]="true"
        [enableZoom]="true"
        [showHeaderInputs]="true"
        [inputFieldsConfig]="{
          agentName: { enabled: true, placeholder: 'Agent Name', required: true },
          agentType: { enabled: true, options: agentTypeOptions, defaultValue: 'individual' },
          metadata: { enabled: true, label: 'Metadata Info', statusText: { saved: 'Metadata Information saved', notSaved: 'Metadata Information not saved' } }
        }"
        (canvasDropped)="onCanvasDropped($event)"
        (nodeSelected)="onNodeSelected($event)"
        (nodeMoved)="onNodeMoved($event)"
        (nodeRemoved)="onDeleteNode($event)"
        (connectionStarted)="onStartConnection($event)"
        (connectionCreated)="onConnectionCreated($event)"
        (stateChanged)="onCanvasStateChanged($event)"
        (agentNameChanged)="onAgentNameChanged($event)"
        (agentTypeChanged)="onAgentTypeChanged($event)"
        (metadataChanged)="onMetadataChanged($event)">

        <!-- Node template for rendering agent nodes -->
        <ng-template #nodeTemplate let-node let-selected="selected" let-onDelete="onDelete"
                     let-onMove="onMove" let-onSelect="onSelect" let-onStartConnection="onStartConnection"
                     let-mouseInteractionsEnabled="mouseInteractionsEnabled" let-canvasMode="canvasMode">
          <app-build-agent-node
            [node]="node"
            [selected]="selected"
            [mouseInteractionsEnabled]="mouseInteractionsEnabled"
            [canvasMode]="canvasMode"
            (deleteNode)="onDelete($event)"
            (moveNode)="onMove($event)"
            (nodeSelected)="onSelect($event)"
            (startConnection)="onStartConnection($event)"
            (nodePositionChanged)="onNodePositionChanged($event)">
          </app-build-agent-node>
        </ng-template>
      </app-canvas-board>
    </div>
  </div>
</div>
