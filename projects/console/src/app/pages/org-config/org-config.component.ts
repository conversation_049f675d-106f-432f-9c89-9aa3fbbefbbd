import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { OrgConfigService } from './services/org-config.service';
import { SelectDropdownComponent, SelectOption } from '../../shared/components/select-dropdown/select-dropdown.component';
import { ButtonComponent } from '../../shared/components/button/button.component';

interface SelectedDropdownValues {
  [key: string]: string;
  org: string;
  domain: string;
  project: string;
  team: string;
}

@Component({
  selector: 'app-org-config',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SelectDropdownComponent, ButtonComponent],
  templateUrl: './org-config.component.html',
  styleUrl: './org-config.component.scss'
})
export class OrgConfigComponent implements OnInit {
  public configForm!: FormGroup;
  public dropdownValues: { [key: string]: SelectOption[] } = {
    org: [],
    domain: [],
    project: [],
    team: [],
  };
  public selectedDropdownValues: SelectedDropdownValues = {
    org: '',
    domain: '',
    project: '',
    team: ''
  };
  public SELECT_DOMAIN = {
    id: 'domainId',
    label: 'Domain',
    selectLabel: 'Select Domain',
  };
  public SELECT_ORGANIZATION = {
    id: 'organizationId',
    label: 'Organization',
    selectLabel: 'Select Organization',
  };
  public SELECT_PROJECT = {
    id: 'projectId',
    label: 'Project',
    selectLabel: 'Select Project',
  };
  public SELECT_TEAM = {
    id: 'teamId',
    label: 'Team',
    selectLabel: 'Select Team',
  };

  constructor(
    private formBuilder: FormBuilder,
    private orgConfigService: OrgConfigService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.configForm = this.getConfigForm();
    this.getDropdownList('org', 0, -1);
  }

  getConfigForm() {
    return this.formBuilder.group({
      org: ['', [Validators.required]],
      domain: ['', [Validators.required]],
      project: ['', [Validators.required]],
      team: ['', [Validators.required]],
    });
  }

  getDropdownList(dropdown: string, level: number, parentId: number) {
    this.orgConfigService.getLevelList(level, parentId).subscribe({
      next: (res: any) => {
        if (res && res.levels) {
          this.dropdownValues[dropdown] = res.levels.map((level: any) => ({
            label: level.name,
            value: level.levelId
          }));
        }
      },
      error: error => console.error(error),
    });
  }

  onDropdownSelect(id: any, level: number, group: string, dropdown: string) {
    const levelId = typeof id === 'object' && id !== null && 'value' in id ? id.value : id;
    const selectedOption = this.dropdownValues[group].find((option: any) => option?.value === levelId);
    this.selectedDropdownValues[group] = selectedOption?.label ?? '';
    if (dropdown !== 'not-found') {
      this.getDropdownList(dropdown, level, levelId);
      this.updateSubDropdown(dropdown);
    }
  }

  updateSubDropdown(dropdown: string) {
    const dropdownList: string[] = ['org', 'domain', 'project', 'team'];
    let startIndex = dropdownList.indexOf(dropdown);
    dropdownList.forEach((key, index) => {
      if (index >= startIndex) {
        this.configForm.get(key)?.setValue(null);
        if (index > startIndex) {
          this.dropdownValues[key] = [];
        }
      }
    });
  }

  isInputValid() {
    return this.configForm.valid;
  }

  onSaveConfig() {
    const usecaseIdPath = Object.values(this.configForm.value).join('@');
    const usecasePath = Object.values(this.selectedDropdownValues)
      .filter(value => value.trim() !== '')
      .join('@');
    const path = `${usecasePath}::${usecaseIdPath}`;
    if (path) {
      this.orgConfigService['tokenStorageService'].setCookie('org_path', path);
      this.router.navigate(['/dashboard']);
    }
  }
} 