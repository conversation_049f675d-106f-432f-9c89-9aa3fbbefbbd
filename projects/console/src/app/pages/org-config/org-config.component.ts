import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { DropdownComponent, DropdownOption } from '../../shared/components/dropdown/dropdown.component';
import { ButtonComponent } from '@ava/play-comp-library';
import { OrgConfigService } from './services/org-config.service';

// Simple interfaces for the API response
interface Team {
  teamId: number;
  teamName: string;
}

interface Project {
  projectId: number;
  projectName: string;
  teams: Team[];
}

interface Domain {
  domainId: number;
  domainName: string;
  projects: Project[];
}

interface Organization {
  orgId: number;
  organizationName: string;
  domains: Domain[];
}

@Component({
  selector: 'app-org-config',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, DropdownComponent, ButtonComponent],
  templateUrl: './org-config.component.html',
  styleUrls: ['./org-config.component.scss']
})
export class OrgConfigComponent implements OnInit {
  configForm: FormGroup;
  
  // Separate arrays for each dropdown to ensure change detection
  orgOptions: DropdownOption[] = [];
  domainOptions: DropdownOption[] = [];
  projectOptions: DropdownOption[] = [];
  teamOptions: DropdownOption[] = [];
  
  private hierarchyData: Organization[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private orgConfigService: OrgConfigService,
    private router: Router
  ) {
    this.configForm = this.formBuilder.group({
      org: ['', Validators.required],
      domain: ['', Validators.required],
      project: ['', Validators.required],
      team: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.orgConfigService.getOrganizationHierarchy().subscribe({
      next: (data: Organization[]) => {
        this.hierarchyData = data;
        this.loadOrganizations();
      },
      error: (error) => {
        console.error('Error loading data:', error);
      }
    });
  }

  loadOrganizations(): void {
    this.orgOptions = this.hierarchyData.map(org => ({
      name: org.organizationName,
      value: org.orgId.toString()
    }));
  }

  onOrgSelect(event: any): void {
    const selectedOrgId = event.selectedOptions?.[0]?.value;
    if (selectedOrgId) {
      this.configForm.patchValue({ org: selectedOrgId });
      this.loadDomains(selectedOrgId);
      // Clear dependent dropdowns
      this.configForm.patchValue({ domain: '', project: '', team: '' });
      this.projectOptions = [];
      this.teamOptions = [];
    }
  }

  loadDomains(orgId: string): void {
    const org = this.hierarchyData.find(o => o.orgId.toString() === orgId);
    if (org) {
      this.domainOptions = org.domains.map(domain => ({
        name: domain.domainName,
        value: domain.domainId.toString()
      }));
    } else {
      this.domainOptions = [];
    }
  }

  onDomainSelect(event: any): void {
    const selectedDomainId = event.selectedOptions?.[0]?.value;
    if (selectedDomainId) {
      this.configForm.patchValue({ domain: selectedDomainId });
      this.loadProjects(selectedDomainId);
      // Clear dependent dropdowns
      this.configForm.patchValue({ project: '', team: '' });
      this.teamOptions = [];
    }
  }

  loadProjects(domainId: string): void {
    const org = this.hierarchyData.find(o => 
      o.domains.some(d => d.domainId.toString() === domainId)
    );
    if (org) {
      const domain = org.domains.find(d => d.domainId.toString() === domainId);
      if (domain) {
        this.projectOptions = domain.projects.map(project => ({
          name: project.projectName,
          value: project.projectId.toString()
        }));
      } else {
        this.projectOptions = [];
      }
    } else {
      this.projectOptions = [];
    }
  }

  onProjectSelect(event: any): void {
    const selectedProjectId = event.selectedOptions?.[0]?.value;
    if (selectedProjectId) {
      this.configForm.patchValue({ project: selectedProjectId });
      this.loadTeams(selectedProjectId);
      // Clear dependent dropdowns
      this.configForm.patchValue({ team: '' });
    }
  }

  loadTeams(projectId: string): void {
    const org = this.hierarchyData.find(o => 
      o.domains.some(d => 
        d.projects.some(p => p.projectId.toString() === projectId)
      )
    );
    if (org) {
      const domain = org.domains.find(d => 
        d.projects.some(p => p.projectId.toString() === projectId)
      );
      if (domain) {
        const project = domain.projects.find(p => p.projectId.toString() === projectId);
        if (project) {
          this.teamOptions = project.teams.map(team => ({
            name: team.teamName,
            value: team.teamId.toString()
          }));
        } else {
          this.teamOptions = [];
        }
      } else {
        this.teamOptions = [];
      }
    } else {
      this.teamOptions = [];
    }
  }

  onTeamSelect(event: any): void {
    const selectedTeamId = event.selectedOptions?.[0]?.value;
    if (selectedTeamId) {
      this.configForm.patchValue({ team: selectedTeamId });
    }
  }

  isFormValid(): boolean {
    return this.configForm.valid;
  }

  onSaveConfig(): void {
    if (this.configForm.valid) {
      const formValues = this.configForm.value;
      
      // Get display names for the path
      const orgName = this.orgOptions.find(opt => opt.value === formValues.org)?.name || '';
      const domainName = this.domainOptions.find(opt => opt.value === formValues.domain)?.name || '';
      const projectName = this.projectOptions.find(opt => opt.value === formValues.project)?.name || '';
      const teamName = this.teamOptions.find(opt => opt.value === formValues.team)?.name || '';

      const usecaseIdPath = Object.values(formValues).join('@');
      const usecasePath = [orgName, domainName, projectName, teamName].filter(name => name).join('@');
      const path = `${usecasePath}::${usecaseIdPath}`;
      
      if (path) {
        this.orgConfigService['tokenStorageService'].setCookie('org_path', path);
        this.router.navigate(['/dashboard']);
      }
    }
  }
}
