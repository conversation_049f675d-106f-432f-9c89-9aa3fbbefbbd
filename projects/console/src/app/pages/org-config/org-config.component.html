<div class="org-config-wrapper">
  <div class="org-config-container">
    <h2>Please select the Realm configuration</h2>
    <form [formGroup]="configForm">
      <div class="dropdown-row">
        <div class="dropdown-group">
          <app-select-dropdown
            [options]="dropdownValues['org']"
            [label]="SELECT_ORGANIZATION.label"
            [placeholder]="SELECT_ORGANIZATION.selectLabel"
            (selectionChange)="onDropdownSelect($event, 1, 'org', 'domain')"
            [dropdownWidth]="'260px'"
            [dropdownHeight]="'56px'"
            formControlName="org"
          ></app-select-dropdown>
        </div>
        <div class="dropdown-group">
          <app-select-dropdown
            [options]="dropdownValues['domain']"
            [label]="SELECT_DOMAIN.label"
            [placeholder]="SELECT_DOMAIN.selectLabel"
            (selectionChange)="onDropdownSelect($event, 2, 'domain', 'project')"
            [dropdownWidth]="'260px'"
            [dropdownHeight]="'56px'"
            formControlName="domain"
          ></app-select-dropdown>
        </div>
        <div class="dropdown-group">
          <app-select-dropdown
            [options]="dropdownValues['project']"
            [label]="SELECT_PROJECT.label"
            [placeholder]="SELECT_PROJECT.selectLabel"
            (selectionChange)="onDropdownSelect($event, 3, 'project', 'team')"
            [dropdownWidth]="'260px'"
            [dropdownHeight]="'56px'"
            formControlName="project"
          ></app-select-dropdown>
        </div>
        <div class="dropdown-group">
          <app-select-dropdown
            [options]="dropdownValues['team']"
            [label]="SELECT_TEAM.label"
            [placeholder]="SELECT_TEAM.selectLabel"
            (selectionChange)="onDropdownSelect($event, 3, 'team', 'not-found')"
            [dropdownWidth]="'260px'"
            [dropdownHeight]="'56px'"
            formControlName="team"
          ></app-select-dropdown>
        </div>
      </div>
      <div class="apply-filters-btn-row" style="display: flex; gap: 1rem; justify-content: center;">
        <app-button variant="primary" size="medium" (buttonClick)="onSaveConfig()" [disabled]="!isInputValid()">Apply Filters</app-button>
      </div>
    </form>
  </div>
</div> 