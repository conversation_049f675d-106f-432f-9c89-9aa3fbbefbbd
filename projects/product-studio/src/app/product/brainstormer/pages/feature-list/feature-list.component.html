<div class="container-fluid p-3" (click)="closeAllDropdowns()">
  <div class="row g-3">
    <!-- g-3 provides gap between columns -->
    <div
      *ngFor="let section of sections"
      class="col-12 col-sm-6 col-lg-3 d-flex flex-column"
    >
      <div class="feature-card-main">
        <!-- Section Main Header (Mo, S, Co, W) -->
        <div class="section-header p-3">
          <!-- Child 1: The title text -->
          <div class="section-title">
            <span>{{ section.subtitle }}</span>
          </div>
          <!-- Child 2: The action button with initials -->
          <div class="section-action">{{ section.title }}</div>
        </div>
        <!-- Drop Zone for Features -->
        <div
          cdkDropList
          [id]="section.id"
          [cdkDropListData]="section.features"
          [cdkDropListConnectedTo]="getSectionIds()"
          (cdkDropListDropped)="onDrop($event)"
          class="feature-list-dropzone border border-top-0 d-flex flex-column gap-3 flex-grow-1"
        >
          <!-- Feature Cards using awe-card -->
          <awe-card
            *ngFor="let feature of section.features"
            [showHeader]="true"
            [showBody]="true"
            [applyHeaderPadding]="true"
            [applyBodyPadding]="true"
            cardClass="feature-item-card"
            cdkDrag
            class="px-3 pt-3"
            (cdkDragStarted)="
              $event.source.element.nativeElement.style.cursor = 'grabbing'
            "
            (cdkDragEnded)="
              $event.source.element.nativeElement.style.cursor = 'grab'
            "
          >
            <!-- Projected Header for awe-card -->
            <div
              awe-card-header-content
              class="d-flex justify-content-between align-items-center"
            >
              <awe-heading
                variant="s2"
                type="bold"
                class="feature-title mb-0 flex-grow-1 pe-2"
                >{{ feature.title }}</awe-heading
              >

              <awe-icons
                (click)="toggleDropdown(feature.id, $event)"
                [iconName]="'three-dot-vertical'"
                [iconColor]="'blue'"
                class="three-dot-icon"
              ></awe-icons>
              <div class="dropdown position-relative">
                <div
                  class="dropdown-menu dropdown-menu-end"
                  [class.show]="isDropdownOpen(feature.id)"
                >
                  <button
                    class="dropdown-item border-buttom"
                    (click)="openEditModal(feature)"
                    type="button"
                  >
                    Edit
                  </button>
                  <button
                    class="dropdown-item text-danger"
                    type="button"
                    (click)="
                      deleteFeature(section.id, feature.id); closeAllDropdowns()
                    "
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>

            <!-- Projected Body for awe-card (Default Slot) -->
            <awe-body-text
              type="body-text"
              class="feature-description text-muted small mb-3 flex-grow-1"
            >
              {{ feature.description }}
            </awe-body-text>

            <span
              *ngFor="let tag of feature.tags"
              class="feature-tag rounded-pill px-2 py-1 m-1"
            >
              {{ tag }}
            </span>
          </awe-card>

          <!-- Empty State for Drop Zone -->
          <div
            *ngIf="section.features.length === 0"
            class="text-center text-muted fst-italic py-4"
          >
            Drag and drop features here
          </div>
        </div>

        <!-- Add More Button -->
        <div class="add-more-section mt-3 text-center p-3">
          <button
            class="add-more-btn w-100"
            (click)="addNewFeature(section.id)"
          >
            Add more
            <awe-icons iconName="awe_plus" class="mt-2"></awe-icons>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Feature Modal -->
<awe-modal
  [isOpen]="isEditModalOpen"
  (closed)="closeEditModal()"
  [showHeader]="true"
  [showFooter]="true"
  width="600px"
  height="auto"
  position="center"
  animation="fade"
  [showCloseButton]="true"
  modalClass="edit-feature-modal"
>
  <!-- Projected Modal Header -->
  <div awe-modal-header class="edit-modal-header">
    <awe-heading variant="s1" type="bold" class="modal-title mb-0">
      {{ isAddingNewFeature ? "Add New Feature" : "Edit Feature" }}
    </awe-heading>
  </div>

  <!-- Projected Modal Body -->
  <div awe-modal-body class="edit-modal-body">
    <h6
      class="feature-title mb-3"
      *ngIf="!isAddingNewFeature && selectedFeatureForEdit"
    >
      {{ selectedFeatureForEdit.title }}
    </h6>
    <h6 class="feature-title mb-3" *ngIf="isAddingNewFeature">
      Create a new feature for this section
    </h6>

    <!-- Feature Title -->
    <div class="row">
      <div class="col-md-12 inp-container mt-2">
        <div class="label">
          <label for="name">Title:</label>
        </div>
        <div class="input-wrapper">
          <awe-input
            variant="fluid"
            id="featureTitle"
            [(ngModel)]="editableFeatureTitle"
            placeholder="Enter feature title"
            class="w-100"
          ></awe-input>
        </div>
      </div>
      <!-- Feature Description -->
      <div class="col-md-12 mt-2">
        <div>
          <label for="name">Description:</label>
        </div>
        <div class="input-wrapper">
          <awe-input
            variant="fluid"
            [expand]="true"
            id="featureDescription"
            [(ngModel)]="editableFeatureDescription"
            placeholder="Enter feature description"
            class="w-100"
          ></awe-input>
        </div>
      </div>
    </div>

    <!-- Editable Tags -->
    <div class="mt-3">
      <label class="form-label fw-medium">Tags</label>
      <div
        *ngFor="
          let tag of editableFeatureTags;
          let i = index;
          trackBy: trackByFn
        "
        class="editable-tag-item d-flex align-items-center mb-2"
      >
        <awe-input
          variant="fluid"
          [icons]="['awe_trash']"
          [required]="true"
          errorMessage="This field is required"
          [(ngModel)]="editableFeatureTags[i]"
          placeholder="Enter tag"
          (iconClickEvent)="removeEditableTag(i)"
          class="w-100"
        >
        </awe-input>
      </div>

      <!-- Add New Tag Button -->
      <button type="button" class="add-new w-100" (click)="addEditableTag()">
        <awe-icons iconName="plus" iconSize="16px" class="me-1"></awe-icons>
        Add Tag
      </button>
    </div>

    <!-- Regenerate Section -->
    <div class="regenerate-section mt-3">
      <awe-heading variant="s2" type="bold">Regenerate with AI</awe-heading>
      <awe-input
        label="Prompt:"
        [expand]="true"
        [(ngModel)]="regeneratePrompt"
        id="regeneratePrompt"
        [icons]="['awe_send']"
        variant="fluid"
        placeholder="Enter prompt to regenerate content..."
        (iconClickEvent)="updateFeature()"
      >
      </awe-input>
    </div>
  </div>

  <!-- Projected Modal Footer -->
  <div awe-modal-footer class="edit-modal-footer">
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button type="button" class="btn-cancel px-5" (click)="closeEditModal()">
        Cancel
      </button>
      <button type="button" class="btn-delete px-5" (click)="updateFeature()">
        {{ isAddingNewFeature ? "Add Feature" : "Update" }}
      </button>
    </div>
  </div>
</awe-modal>
