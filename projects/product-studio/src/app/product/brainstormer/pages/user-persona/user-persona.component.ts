import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { Subscription } from 'rxjs';

import {
  PersonaDataService,
  PersonaData,
} from '../../services/persona-data.service';

// Import AWE Components (adjust paths as needed)
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import { HeadingComponent, IconsComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-user-persona',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AweCardComponent,
    AweModalComponent,
    HeadingComponent,
    IconsComponent,
  ],
  templateUrl: './user-persona.component.html',
  styleUrls: ['./user-persona.component.scss'],
})
export class UserPersonaComponent implements OnInit, OnDestroy {
  // Output events
  @Output() personaSelected = new EventEmitter<string>();

  // Icons
  editIcon: string = '/icons/awe_edit.svg';
  deleteIcon: string = '/svgs/delete-icon2.svg';
  colonIcon: string = '/svgs/colon.svg';
  threeDotsIcon: string = '/icons/three-dot.svg';
  awe_delete: string = '/icons/awe-delete.svg';

  // Avatars
  salesAvatar: string = '/svgs/sales-avatar.svg';
  managerAvatar: string = '/svgs/manager-avatar.svg';
  developerAvatar: string = '/svgs/developer-avatar.svg';
  teacherAvatar: string = '/svgs/teacher-avatar.svg';
  designerAvatar: string = '/svgs/designer-avatar.svg';

  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 5;

  // Modal state
  isModalOpen = false;
  modalMode: 'add' | 'edit' | 'delete' = 'add';
  editablePersona: Partial<PersonaData> = {};
  personalityInput: string = '';
  regeneratePrompt: string = '';
  selectedPersonaForEdit: PersonaData | null = null;
  // Dropdown state
  openDropdownId: string | null = null;
  personas: PersonaData[] = [];
  private subscription = new Subscription();
  // --- Delete Modal State ---
  isDeleteModalOpen = false;
  personaToDelete: PersonaData | null = null;
  deleteBadgeCount: number = 1; // You can set this dynamically as needed

  constructor(
    private personaDataService: PersonaDataService
  ) {}
  ngOnInit(): void {
    // Subscribe to personas data
    this.subscription.add(
      this.personaDataService.personas$.subscribe((personas) => {
        this.personas = personas;
      }),
    );
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  // --- Getters ---
  get currentPagePersonas(): PersonaData[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return this.personas.slice(startIndex, startIndex + this.itemsPerPage);
  }

  get totalPages(): number {
    return Math.ceil(this.personas.length / this.itemsPerPage);
  }

  // // --- Dropdown Methods ---
  // toggleDropdown(personaId: string): void {
  //   this.openDropdownId = this.openDropdownId === personaId ? null : personaId;
  // }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  // isDropdownOpen(personaId: string): boolean {
  //   return this.openDropdownId === personaId;
  // }

  // --- Modal Methods ---
  openAddModal(): void {
    this.modalMode = 'add';
    this.selectedPersonaForEdit = null;
    this.resetEditablePersona();
    this.isModalOpen = true;
    // this.closeAllDropdowns();
  }

  // openEditModal(persona: PersonaData): void {
  //   this.modalMode = 'edit';
  //   this.selectedPersonaForEdit = persona;
  //   this.editablePersona = { ...persona };
  //   this.personalityInput = persona.personality.join(', ');
  //   this.isModalOpen = true;
  //   // this.closeAllDropdowns();
  // }

  closeModal(): void {
    this.isModalOpen = false;
    this.selectedPersonaForEdit = null;
    this.resetEditablePersona();
  }

  // --- CRUD Methods ---
  savePersona(form: NgForm): void {
    if (!form.valid) return;

    const personalityArray = this.personalityInput
      .split(',')
      .map((t) => t.trim())
      .filter(Boolean);

    if (this.modalMode === 'add') {
      const newPersonaData =
        this.personaDataService.convertUserPersonaToPersonaData({
          ...this.editablePersona,
          personality: personalityArray,
          avatar: this.personaDataService.getDefaultAvatar(
            this.editablePersona.role || '',
          ),
        });
      this.personaDataService.addPersona(newPersonaData);
      this.currentPage = this.totalPages;
    } else if (this.modalMode === 'edit' && this.selectedPersonaForEdit) {
      this.personaDataService.updatePersona(this.selectedPersonaForEdit.id, {
        ...this.editablePersona,
        personality: personalityArray,
      });
    }

    this.closeModal();
  }

  openDeleteModal(persona: PersonaData): void {
    this.isDeleteModalOpen = true;
    this.personaToDelete = persona;
    this.deleteBadgeCount = 1; // Or set to a relevant value, e.g., 1 or personas.length
  }

  closeDeleteModal(): void {
    this.isDeleteModalOpen = false;
    this.personaToDelete = null;
  }

  confirmDeletePersona(): void {
    if (this.personaToDelete) {
      this.personaDataService.deletePersona(this.personaToDelete.id);
    }
    this.closeDeleteModal();
  }

  // --- Regenerate Method ---
  onRegenerate(): void {
    if (!this.regeneratePrompt.trim()) {
      console.log('Regenerate prompt is empty.');
      return;
    }

    console.log('Regenerating with prompt:', this.regeneratePrompt);
    // In a real application, you would add logic here to:
    // 1. Show a loading spinner.
    // 2. Call an API service with the prompt.
    // 3. Update the form fields with the data returned from the API.
    // 4. Hide the loading spinner.

    // For now, we'll just log it and clear the field.
    this.regeneratePrompt = '';
  }

  private resetEditablePersona(): void {
    this.editablePersona = {};
    this.personalityInput = '';
    this.regeneratePrompt = '';
  }
  // // --- Pagination Methods ---
  // previousPage(): void {
  //   if (this.currentPage > 1) this.currentPage--;
  // }

  // nextPage(): void {
  //   if (this.currentPage < this.totalPages) this.currentPage++;
  // }

  trackByPersona(_index: number, persona: PersonaData): string {
    return persona.id;
  }

  // Navigation method
  onPersonaClick(persona: PersonaData): void {
    this.personaDataService.setSelectedPersona(persona.id);
    this.personaSelected.emit(persona.id);
  }
}
