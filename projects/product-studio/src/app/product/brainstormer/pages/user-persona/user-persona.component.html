<div class="container-flui" (click)="closeAllDropdowns()">
  <!-- Header Section -->
  <div class="d-flex justify-content-end align-items-center mb-1">
    <button class="btn-add-new m-2" (click)="openAddModal()">
      Add new <span class="plus-icon">+</span>
    </button>
  </div>

  <!-- Personas Grid -->
  <div
    class="row gap-4 d-flex justify-content-center align-content-center flex-wrap"
    *ngIf="personas.length > 0; else noPersonas"
  >
    <div
      *ngFor="let persona of currentPagePersonas; trackBy: trackByPersona"
      class="persona-card-wrapper col-lg-2 col-md-3 col-sm-6 col-6 mb-3 px-1"
      (click)="onPersonaClick(persona)"
      style="cursor: pointer"
    >
      <awe-card [applyBodyPadding]="false" cardClass="persona-card">
        <!-- Three Dots Dropdown -->
        <div class="card-actions">
          <button
            class="three-dots-btn"
            (click)="$event.stopPropagation(); openDeleteModal(persona)"
            aria-label="More actions"
          >
            <awe-icons iconName="awe_trash" iconSize="20px"></awe-icons>
          </button>
        </div>

        <!-- Card Body -->
        <div class="p-4">
          <!-- Profile Section with Avatar background -->
          <div class="profile-section text-center">
            <div class="avatar-wrapper mx-auto">
              <img
                [src]="persona.avatar"
                [alt]="persona.name || persona.role"
                class="avatar"
              />
            </div>
            <h2 class="role-title">{{ persona.name || persona.role }}</h2>
          </div>

          <!-- Info Section -->
          <div class="info-section">
            <div class="info-row">
              <span class="info-label">Age</span
              ><span class="info-value">{{ persona.age }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Education</span
              ><span class="info-value">{{ persona.education }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Status</span
              ><span class="info-value">{{ persona.status }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Location</span
              ><span class="info-value">{{ persona.location }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Tech Literacy</span
              ><span class="info-value">{{ persona.techLiteracy }}</span>
            </div>
          </div>

          <!-- Quote Section -->
          <div class="quote-section">
            <img [src]="colonIcon" alt="Quote" class="quote-icon" />
            <p class="quote-text">{{ persona.quote }}</p>
          </div>

          <!-- Personality Section -->
          <div class="personality-section">
            <h3 class="personality-title">Personality</h3>
            <div class="personality-tags">
              <span
                *ngFor="let trait of persona.personality"
                class="personality-tag"
                >{{ trait }}</span
              >
            </div>
          </div>
        </div>
      </awe-card>
    </div>
  </div>

  <ng-template #noPersonas>
    <div class="text-center p-5 text-muted">
      No user personas found. Click 'Add new' to start.
    </div>
  </ng-template>

  <!-- Pagination Section -->
  <!-- <div class="pagination-section">
    <button
      class="pagination-arrow"
      [disabled]="currentPage === 1"
      (click)="previousPage()"
    >
      ‹
    </button>
    <button
      class="pagination-arrow"
      [disabled]="currentPage === totalPages"
      (click)="nextPage()"
    >
      ›
    </button>
  </div> -->
</div>

<!-- old  Add modal -->
<awe-modal
  [isOpen]="isModalOpen"
  [showHeader]="true"
  [showFooter]="true"
  [width]="'600px'"
  [maxWidth]="'90vw'"
  (closed)="closeModal()"
>
  <!-- All Modal content remains exactly the same as your original code -->
  <div
    awe-modal-header
    class="d-flex justify-content-between align-items-center"
  >
    <awe-heading variant="h5" type="bold"> Add New Persona </awe-heading>
  </div>
  <div awe-modal-body>
    <form #personaForm="ngForm" (ngSubmit)="savePersona(personaForm)">
      <div class="form-row">
        <label for="name" class="form-label">Name</label>
        <input
          type="text"
          id="name"
          class="form-control"
          name="name"
          [(ngModel)]="editablePersona.name"
          required
        />
      </div>
      <div class="form-row">
        <label for="role" class="form-label">Role</label>
        <input
          type="text"
          id="role"
          class="form-control"
          name="role"
          [(ngModel)]="editablePersona.role"
          required
        />
      </div>
      <div class="form-row">
        <label for="age" class="form-label">Age</label>
        <input
          type="number"
          id="age"
          class="form-control"
          name="age"
          [(ngModel)]="editablePersona.age"
          required
        />
      </div>
      <div class="form-row">
        <label for="education" class="form-label">Education</label>
        <input
          type="text"
          id="education"
          class="form-control"
          name="education"
          [(ngModel)]="editablePersona.education"
          required
        />
      </div>
      <div class="form-row">
        <label for="status" class="form-label">Status</label>
        <select
          id="status"
          class="form-control"
          name="status"
          [(ngModel)]="editablePersona.status"
          required
        >
          <option value="Single">Single</option>
          <option value="Married">Married</option>
          <option value="Divorced">Divorced</option>
        </select>
      </div>
      <div class="form-row">
        <label for="location" class="form-label">Location</label>
        <input
          type="text"
          id="location"
          class="form-control"
          name="location"
          [(ngModel)]="editablePersona.location"
          required
        />
      </div>
      <div class="form-row">
        <label for="techLiteracy" class="form-label">Tech Literacy</label>
        <select
          id="techLiteracy"
          class="form-control"
          name="techLiteracy"
          [(ngModel)]="editablePersona.techLiteracy"
          required
        >
          <option value="Low">Low</option>
          <option value="Medium">Medium</option>
          <option value="High">High</option>
        </select>
      </div>
      <div class="form-row-full">
        <label for="quote" class="form-label">Quote</label>
        <textarea
          id="quote"
          class="form-control"
          name="quote"
          rows="3"
          [(ngModel)]="editablePersona.quote"
          required
        ></textarea>
      </div>
      <div class="form-row-full">
        <label for="personality" class="form-label">Personality</label>
        <input
          type="text"
          id="personality"
          class="form-control"
          name="personality"
          [(ngModel)]="personalityInput"
          placeholder="Introvert, Thinker, Spender"
          required
        />
        <small class="form-text"
          >Enter the personalities, separating with a comma.</small
        >
      </div>
      <!-- Regenerate Section -->
      <div class="regenerate-section mt-4">
        <label for="regeneratePrompt" class="form-label fw-medium"
          >Regenerate</label
        >
        <div class="input-group">
          <input
            type="text"
            id="regeneratePrompt"
            class="form-control form-control-sm"
            [(ngModel)]="regeneratePrompt"
            name="regeneratePrompt"
            placeholder="Type your prompt here..."
          />
          <button
            class="btn btn-sm btn-outline-secondary"
            type="button"
            (click)="onRegenerate()"
          >
            <awe-icons iconName="send" iconSize="16px"></awe-icons>
          </button>
        </div>
      </div>
    </form>
  </div>
  <div
    awe-modal-footer
    class="d-flex justify-content-end align-items-center gap-3"
  >
    <button type="button" class="btn-cancel px-5" (click)="closeModal()">
      Cancel
    </button>
    <button
      type="button"
      class="btn-delete px-5"
      (click)="savePersona(personaForm)"
      [disabled]="!personaForm.valid"
    >
      {{ modalMode === "edit" ? "Update" : "Add Persona" }}
    </button>
  </div>
</awe-modal>

<!-- New Add modal -->
<awe-modal
  [isOpen]="isModalOpen"
  [showHeader]="true"
  [showFooter]="true"
  [width]="'600px'"
  [maxWidth]="'90vw'"
  (closed)="closeModal()"
>
  <div class="mb-2" awe-modal-header>
    <awe-heading variant="s1" type="bold">{{
      modalMode === "edit" ? "Edit User Biography" : "Add New Persona"
    }}</awe-heading>
  </div>

  <div awe-modal-body>
    <div class="modal-form">
      <!-- Profile Data -->
      <div *ngIf="selectedCardForEdit?.type === 'profile'">
        <div class="profile-data row g-3">
          <div class="col-md-12 inp-container">
            <div class="label">
              <label for="name">Name:</label>
            </div>
            <div class="input-wrapper">
              <awe-input
                variant="fluid"
                label="Name:"
                [(ngModel)]="editData.name"
                placeholder="Enter name"
                class="w-100"
              ></awe-input>
            </div>
          </div>
          <div class="col-md-12 inp-container">
            <div class="label">
              <label for="role">Role:</label>
            </div>
            <div class="input-wrapper">
              <awe-input
                variant="fluid"
                label="Role:"
                [(ngModel)]="editData.role"
                placeholder="Enter role"
                class="w-100"
              ></awe-input>
            </div>
          </div>
          <div class="col-md-12 inp-container">
            <div class="label">
              <label for="age">Age:</label>
            </div>
            <div class="input-wrapper">
              <awe-input
                variant="fluid"
                label="Age:"
                [(ngModel)]="editData.age"
                type="number"
                placeholder="Enter age"
                class="w-100"
              ></awe-input>
            </div>
          </div>
          <div class="col-md-12 inp-container">
            <div class="label">
              <label for="education">Education:</label>
            </div>
            <div class="input-wrapper">
              <awe-input
                label="Education:"
                variant="fluid"
                [(ngModel)]="editData.education"
                placeholder="Enter education"
                class="w-100"
              ></awe-input>
            </div>
          </div>
          <div class="col-md-12 inp-container">
            <div class="label">
              <label for="status">Status:</label>
            </div>
            <div class="input-wrapper">
              <awe-input
                label="Status:"
                variant="fluid"
                [(ngModel)]="editData.status"
                placeholder="Enter status"
                class="w-100"
              ></awe-input>
            </div>
          </div>
          <div class="col-md-12 inp-container">
            <div class="label">
              <label for="location">Location:</label>
            </div>
            <div class="input-wrapper">
              <awe-input
                label="Location:"
                variant="fluid"
                [(ngModel)]="editData.location"
                placeholder="Enter location"
                class="w-100"
              ></awe-input>
            </div>
          </div>
          <div class="col-md-12 inp-container">
            <div class="label">
              <label for="techLiteracy">Tech Literacy:</label>
            </div>
            <div class="input-wrapper">
              <awe-input
                label="Tech Literacy:"
                variant="fluid"
                [(ngModel)]="editData.techLiteracy"
                placeholder="Enter tech literacy"
                class="w-100"
              ></awe-input>
            </div>
          </div>
          <div class="col-12 inp-container">
            <div class="label">
              <label for="quote">Quote:</label>
            </div>
            <div class="input-wrapper">
              <awe-input
                label="Quote:"
                variant="fluid"
                [(ngModel)]="editData.quote"
                placeholder="Enter quote"
                class="w-100"
              ></awe-input>
            </div>
          </div>
        </div>
      </div>

      <!-- Array Data (Pain Points, Goals, etc.) -->
      <div
        *ngIf="
          isArrayData(editData) &&
          selectedCardForEdit?.type !== 'skills' &&
          selectedCardForEdit?.type !== 'devices'
        "
      >
        <div
          *ngFor="let item of editData; let i = index; trackBy: trackByIndex"
          class="mb-3"
        >
          <div class="d-flex gap-2">
            <awe-input
              variant="fluid"
              [(ngModel)]="editData[i]"
              type="text"
              [icons]="['awe_trash']"
              [placeholder]="
                'Enter ' + selectedCardForEdit?.title?.toLowerCase() + ' item'
              "
              class="flex-grow-1"
              (iconClickEvent)="removeArrayItem(i)"
            >
            </awe-input>
          </div>
        </div>
        <button (click)="addArrayItem()" class="add-new">Add new +</button>
      </div>

      <!-- Skills Data -->
      <div *ngIf="selectedCardForEdit?.type === 'skills'">
        <div
          *ngFor="let skill of editData; let i = index; trackBy: trackByIndex"
          class="mb-3"
        >
          <label for="skillName">Skill Name:</label>
          <awe-input
            variant="fluid"
            [icons]="['awe_trash']"
            [required]="true"
            errorMessage="This field is required"
            [(ngModel)]="skill.name"
            placeholder="Skill name"
            (iconClickEvent)="removeArrayItem(i)"
            class="w-100"
          >
          </awe-input>
          <div class="input-wrapper">
            <awe-slider
              label="Level"
              [(ngModel)]="skill.level"
              mobileSize="small"
              tabletSize="medium"
              desktopSize="large"
              touchTargetSize="44px"
              [showTicks]="true"
              [customTickValues]="[0, 25, 50, 75, 100]"
              variant="primary"
              (dragStart)="onDragStart(i)"
              (dragEnd)="onDragEnd(i)"
              [min]="0"
              [max]="100"
            ></awe-slider>
          </div>
        </div>
        <button (click)="addArrayItem()" class="mb-3 add-new">
          Add Skills+
        </button>
      </div>

      <!-- Devices Data -->
      <div *ngIf="selectedCardForEdit?.type === 'devices'">
        <div class="mb-3">
          <label class="form-label">Available Devices</label>
          <div class="form-check">
            <input
              class="form-check-input"
              type="checkbox"
              [checked]="editData.includes('mobile')"
              (change)="toggleDevice('mobile')"
              id="deviceMobile"
            />
            <label class="form-check-label" for="deviceMobile"> Mobile </label>
          </div>
          <div class="form-check">
            <input
              class="form-check-input"
              type="checkbox"
              [checked]="editData.includes('laptop')"
              (change)="toggleDevice('laptop')"
              id="deviceLaptop"
            />
            <label class="form-check-label" for="deviceLaptop"> Laptop </label>
          </div>
          <div class="form-check">
            <input
              class="form-check-input"
              type="checkbox"
              [checked]="editData.includes('tablet')"
              (change)="toggleDevice('tablet')"
              id="deviceTablet"
            />
            <label class="form-check-label" for="deviceTablet"> Tablet </label>
          </div>
        </div>
      </div>

      <!-- Regenerate Section -->
      <div class="regenerate-section mt-3">
        <awe-heading variant="h6" type="bold">Regenerate with AI</awe-heading>

        <awe-input
          label="Prompt:"
          [expand]="true"
          [(ngModel)]="regeneratePrompt"
          [icons]="['awe_send']"
          variant="fluid"
          placeholder="Enter prompt to regenerate content..."
          (iconClickEvent)="onRegenerate()"
          class="mb-2"
        >
        </awe-input>
      </div>
    </div>
  </div>

  <div awe-modal-footer>
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button type="button" class="btn-cancel px-5" (click)="closeEditModal()">
        Cancel
      </button>
      <button type="button" class="btn-delete px-5" (click)="saveCardData()">
        Update
      </button>
    </div>
  </div>
</awe-modal>

<!-- Delete Confirmation Modal -->
<awe-modal
  class="delete-modal"
  [isOpen]="isDeleteModalOpen"
  modalClass="delete-modal-custom"
>
  <div
    awe-modal-body
    class="d-flex gap-3 mb-3 flex-column align-items-center justify-content-center py-2"
  >
    <div
      class="delete-icon-wrapper d-flex justify-content-center align-items-center mb-3"
    >
      <!-- <awe-icons
        iconName="awe_delete"
        iconSize="64px"
        class="delete-icon"
      ></awe-icons> -->
      <img [src]="awe_delete" alt="Delete" class="delete-icon" />
    </div>
    <awe-heading variant="s2" type="regular" class="text-center mb-4">
      Are you sure you want to delete this ?
    </awe-heading>
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button
        type="button"
        class="btn-cancel px-5"
        (click)="closeDeleteModal()"
      >
        Cancel
      </button>
      <button
        type="button"
        class="btn-delete px-5"
        (click)="confirmDeletePersona()"
      >
        Delete
      </button>
    </div>
  </div>
</awe-modal>
