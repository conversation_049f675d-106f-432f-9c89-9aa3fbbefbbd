import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  CdkDragDrop,
  DragDropModule,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import {
  HeadingComponent,
  IconsComponent,
  BodyTextComponent,
  InputComponent,
  SliderComponent,
} from '@awe/play-comp-library';
import { DropdownItem } from '../../components/edit-dialog/edit-dialog.component';
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';

interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
  impact: number; // 0-100 for progress bar
  priority: number; // 0-100 for progress bar
}

interface FeatureSection {
  id: string;
  title: string;
  subtitle: string;
  features: FeatureCard[];
}

@Component({
  selector: 'app-swot-analysis',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    HeadingComponent,
    DragDropModule,
    AweCardComponent,
    IconsComponent,
    BodyTextComponent,
    AweModalComponent,
    InputComponent,
    SliderComponent,
  ],
  templateUrl: './swot-analysis.component.html',
  styleUrl: './swot-analysis.component.scss',
})
export class SwotAnalysisComponent implements OnInit {
  roboBallIcon: string = '/icons/robo_ball.svg';
  threeDotsIcon: string = 'icons/three-dot.svg';
  trashIcon: string = '/icons/awe_trash.svg';
  editIcon: string = '/icons/awe_edit.svg';

  // Modal State
  isEditModalOpen = false;
  selectedFeatureForEdit: FeatureCard | null = null;
  isAddingNewFeature = false; // Track if we're adding new or editing existing
  currentSectionId: string = ''; // Track which section we're adding to

  // Create a working copy for editing to avoid direct mutation until save
  editableFeatureTitle: string = '';
  editableFeatureDescription: string = '';
  editableFeatureTags: string[] = [''];
  regeneratePrompt: string = '';
  editableFeatureImpact: number = 75;
  editableFeaturePriority: number = 75;

  dropdownItems: DropdownItem[] = [
    { label: 'Edit', action: 'edit', icon: this.editIcon },
    { label: 'Delete', action: 'delete', icon: this.trashIcon },
  ];

  currentEditingFeature: FeatureCard | null = null;
  openDropdownId: string | null = null;

  sections: FeatureSection[] = [
    {
      id: 'strengths',
      title: 'S',
      subtitle: 'STRENGTHS',
      features: [
        {
          id: 'strength-1',
          title: 'Unique Biometric Authentication',
          description: '',
          tags: ['Impact', 'Security', 'Innovation'],
          impact: 85,
          priority: 90,
        },
        {
          id: 'strength-2',
          title: 'Advanced Security Features',
          description:
            'Multi-layer security with biometric verification and encryption',
          tags: ['Security', 'Technology', 'Trust'],
          impact: 92,
          priority: 88,
        },
      ],
    },
    {
      id: 'weaknesses',
      title: 'W',
      subtitle: 'WEAKNESSES',
      features: [
        {
          id: 'weakness-1',
          title: 'High Manufacturing Cost',
          description:
            'Biometric sensors increase production costs significantly',
          tags: ['Cost', 'Manufacturing', 'Budget'],
          impact: 75,
          priority: 80,
        },
        {
          id: 'weakness-2',
          title: 'Limited Merchant Adoption',
          description:
            'Requires specialized terminals for biometric verification',
          tags: ['Adoption', 'Infrastructure', 'Market'],
          impact: 70,
          priority: 85,
        },
      ],
    },
    {
      id: 'opportunities',
      title: 'O',
      subtitle: 'OPPORTUNITIES',
      features: [
        {
          id: 'opportunity-1',
          title: 'Growing Security Concerns',
          description:
            'Increasing demand for secure payment methods due to fraud',
          tags: ['Market', 'Security', 'Demand'],
          impact: 88,
          priority: 92,
        },
        {
          id: 'opportunity-2',
          title: 'Digital Payment Growth',
          description:
            'Rapid expansion of contactless payment adoption globally',
          tags: ['Growth', 'Digital', 'Global'],
          impact: 82,
          priority: 78,
        },
      ],
    },
    {
      id: 'threats',
      title: 'T',
      subtitle: 'THREATS',
      features: [
        {
          id: 'threat-1',
          title: 'Competitive Technology',
          description:
            'Alternative authentication methods like facial recognition',
          tags: ['Competition', 'Technology', 'Innovation'],
          impact: 65,
          priority: 75,
        },
        {
          id: 'threat-2',
          title: 'Privacy Regulations',
          description:
            'Strict biometric data protection laws may limit adoption',
          tags: ['Regulation', 'Privacy', 'Compliance'],
          impact: 78,
          priority: 82,
        },
      ],
    },
  ];

  constructor() {}

  getSectionIds(): string[] {
    return this.sections.map((section) => section.id);
  }

  onNext(): void {
    // Handle next button click
  }

  onDrop(event: CdkDragDrop<FeatureCard[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    }
  }

  toggleDropdown(featureId: string, event: Event): void {
    event.stopPropagation();

    // If clicking on the same dropdown, close it
    if (this.openDropdownId === featureId) {
      this.openDropdownId = null;
    } else {
      // Open the clicked dropdown and close others
      this.openDropdownId = featureId;
    }
  }

  isDropdownOpen(featureId: string): boolean {
    return this.openDropdownId === featureId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  addNewFeature(sectionId: string): void {
    // Open modal for adding new SWOT item
    this.isAddingNewFeature = true;
    this.currentSectionId = sectionId;
    this.selectedFeatureForEdit = null;
    // Clear editable data for new feature
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [''];
    this.regeneratePrompt = '';
    this.editableFeatureImpact = 75;
    this.editableFeaturePriority = 75;
    this.isEditModalOpen = true;
  }

  openEditModal(feature: FeatureCard): void {
    this.isAddingNewFeature = false; // We're editing, not adding
    this.selectedFeatureForEdit = { ...feature }; // Edit a copy
    this.currentSectionId = ''; // Not needed for editing
    // Set up editable data
    this.editableFeatureTitle = feature.title;
    this.editableFeatureDescription = feature.description;
    this.editableFeatureTags = [...feature.tags]; // Copy array
    this.regeneratePrompt = '';
    this.editableFeatureImpact = feature.impact ?? 75;
    this.editableFeaturePriority = feature.priority ?? 75;
    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedFeatureForEdit = null;
    this.isAddingNewFeature = false;
    this.currentSectionId = '';
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [''];
    this.regeneratePrompt = '';
    this.editableFeatureImpact = 75;
    this.editableFeaturePriority = 75;
  }

  onDropdownAction(action: string, feature: FeatureCard): void {
    this.closeAllDropdowns();

    switch (action) {
      case 'edit':
        this.openEditModal(feature);
        break;
      case 'delete':
        this.deleteFeature(feature.id);
        break;
    }
  }

  deleteFeature(featureId: string): void {
    for (const section of this.sections) {
      const index = section.features.findIndex((f) => f.id === featureId);
      if (index > -1) {
        section.features.splice(index, 1);
        break;
      }
    }
  }

  updateFeature(): void {
    if (this.isAddingNewFeature) {
      // Add new feature to the specified section
      const section = this.sections.find((s) => s.id === this.currentSectionId);
      if (section) {
        const newFeature: FeatureCard = {
          id: `${this.currentSectionId}-${Date.now()}`,
          title: this.editableFeatureTitle,
          description: this.editableFeatureDescription,
          tags: this.editableFeatureTags.filter((tag) => tag.trim() !== ''),
          impact: this.editableFeatureImpact,
          priority: this.editableFeaturePriority,
        };
        section.features.push(newFeature);
      }
    } else if (this.selectedFeatureForEdit) {
      // Update existing feature
      for (const section of this.sections) {
        const featureIndex = section.features.findIndex(
          (f) => f.id === this.selectedFeatureForEdit!.id,
        );
        if (featureIndex > -1) {
          section.features[featureIndex] = {
            ...section.features[featureIndex],
            title: this.editableFeatureTitle,
            description: this.editableFeatureDescription,
            tags: this.editableFeatureTags.filter((tag) => tag.trim() !== ''),
            impact: this.editableFeatureImpact,
            priority: this.editableFeaturePriority,
          };
          break;
        }
      }
    }

    this.closeEditModal();
  }

  // Methods for managing editable tags in the modal
  addEditableTag(): void {
    this.editableFeatureTags.push(''); // Add a new empty string to edit
  }

  removeEditableTag(index: number): void {
    if (this.editableFeatureTags.length > 1) {
      this.editableFeatureTags.splice(index, 1);
    }
  }

  trackByIndex(index: number): number {
    return index;
  }

  // Method to open modal from card menu
  openDialogFromCard(feature: FeatureCard): void {
    this.openEditModal(feature);
  }

  ngOnInit(): void {
    // Add click listener to close dropdowns when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown')) {
        this.closeAllDropdowns();
      }
    });
  }

  // Helper methods for progress bars
  getImpactWidth(feature: FeatureCard): string {
    return `${feature.impact}%`;
  }

  getPriorityWidth(feature: FeatureCard): string {
    return `${feature.priority}%`;
  }

  getImpactColor(impact: number): string {
    if (impact >= 80) return '#6566CD'; // Green
    if (impact >= 60) return '#6566CD'; // Yellow
    return '#6566CD'; // Red
  }

  getPriorityColor(priority: number): string {
    if (priority >= 80) return '#6566CD'; // Blue
    if (priority >= 60) return '#6566CD'; // Purple
    return '#6566CD'; // Gray
  }
}
