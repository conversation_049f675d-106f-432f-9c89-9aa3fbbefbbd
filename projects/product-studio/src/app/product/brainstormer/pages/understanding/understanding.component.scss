.column-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 0.5rem; /* Space between cards */
  flex-wrap: wrap; /* Allow cards to wrap to the next line */
  overflow: hidden; /* Prevent overflow of cards */
  align-items: stretch; /* Ensure all cards stretch to the same height */
  justify-content: space-between; /* Distribute space evenly between cards */

  &.first-column,
  &.third-column {
    flex-direction: column;
    gap: 0.5rem;
    > awe-card {
      flex: 1;
    }
  }
  &.second-column {
    flex-direction: row;
    gap: 0.5rem;
    > awe-card {
      flex: 1;
    }
  }
}
:host ::ng-deep awe-heading {
  color: #0047bc;
}

// image positioning style

.solution-content-wrapper {
  align-items: flex-start;
  padding-left: 0;
  gap: 0.25rem; /* Space between text and image */
}


.key-partners-wrapper{
 align-items: flex-start;
  padding-left: 0;
  gap: 0.25rem; 
}


.flex-container{
  display: flex;
  flex-direction: column;
  align-items: end;

}

.solution-text {
  flex: 1;
  padding-left: 1rem;
  min-width: 0; /* Allows text to wrap properly */
}

.img-position {
  display: flex;
  align-items: end;
  justify-content: end;
  width: 138px;
  height: 138px;
  // margin-top: 2.5rem;
  flex-shrink: 0; /* Prevents image from shrinking */
}

.card-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.bullet-point {
  display: flex;
  align-items: flex-start;
}
.bullet-point::before {
  content: "•";
  margin-right: 8px;
  margin-top: 2px; /* Adjust based on your caption component */
  color: #666;
  flex-shrink: 0;
}

/* Optional: Add some spacing between problem and solution cards */
.problem-card {
  margin-bottom: 1rem;
}
// .key-partners{
//   background: url(assets/icons/key-partners.png) lightgray 50% / cover no-repeat;
// }

.card-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .icon-button {
    background: none;
    border: none;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: start;
    padding: 0;
    min-width: 0;

    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

:host ::ng-deep .awe-card-body ul li {
  line-height: 1.4;
  word-wrap: break-word;
  overflow: hidden;
}

@media (min-width: 990px) and (max-width: 1440px) {
  .column-container {
    &.first-column,
    &.third-column {
      min-height: 100%;
    }
  }

  .column-container.second-column {
    flex-direction: column; // Stack cards vertically
    gap: 0.5rem;
    height: 100%;
    overflow: hidden; // Allow scrolling if content overflows

    > awe-card {
      width: 100%;
      // max-height: 32%;
      min-height: 300px; // Ensure consistent height
      max-height: 300px; // Prevent cards from growing too large
      flex: none;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Medium screens (md) - 768px to 991.98px
@media (max-width: 991.98px) and (min-width: 768px) {
  .canvas-row {
    flex-direction: coloum;
  }

  .column-container {
    &.first-column,
    &.third-column {
      flex-direction: row;
      gap: 0.5rem;
    }
    &.second-column {
      // Cards stack vertically
      flex-direction: column;
      max-height: 32%;
      gap: 0; //use margin on cards instead of gap for vertical stacking

      > awe-card {
        max-width: 100%;
        margin-bottom: 0.5rem;
        flex-grow: 0; // Don't grow to fill vertical space
        flex-shrink: 0;
        flex-basis: auto;
        overflow: hidden; // Prevent overflow
        height: 100%; // Ensure cards take full height of the column
        min-height: 300px; // Ensure consistent height
        max-height: 300px; // Prevent cards from growing too large

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // Adjust font size in projected header for MD screens
  .custom-card-header h6 {
    font-size: 0.8rem;
  }
}

// Small screens (sm) - max-width 767.98px
@media (max-width: 767.98px) {
  .column-container {
    flex-direction: column; // Stack all cards vertically
    gap: 0; // Use margin on cards instead
    height: 100%;

    &.first-column,
    &.second-column,
    &.third-column {
      flex-direction: column;
      gap: 0; // Use margin on cards

      > awe-card {
        width: 100%; // Same width for all cards
        height: 200px; // Fixed height for all cards
        min-height: 300px; // Ensure consistent height
        max-height: 200px; // Prevent cards from growing
        margin-bottom: 0.5rem;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Extra small screens (xs) - max-width 575.98px
@media (max-width: 575.98px) {
  .column-container {
    &.first-column,
    &.second-column,
    &.third-column {
      > awe-card {
        width: 100%; // Same width for all cards
        height: 100%; // Smaller fixed height for extra small screens
        min-height: 300px; // Ensure consistent height
        max-height: 300px; // Prevent cards from growing
        margin-bottom: 0.5rem;
      }
    }
  }

  // Adjust styles for projected content on XS screens
  .custom-card-header h6 {
    font-size: 0.7rem;
  }

  .card-icon {
    // Projected icon
    width: 32px;
    height: 32px;

    .card-icon-img {
      width: 20px;
      height: 20px;
    }
  }
}

// Modal SCSS

.edit-modal-header {
  .modal-title {
    font-weight: 600; // Example
  }
}

.edit-modal-body {
  min-height: 370px;
  max-height: auto;
   ̰ .item-title {
    font-weight: 500;
    color: #555; // Example
  }

  .editable-data-item {
    display: flex;
    align-items: center;
    justify-content: space-between; /* Align items with space between */
    padding: 0.3rem;
    margin-right: 1rem;
    border-radius: 8px;
    border: 0.7px solid #e1e1e1;
    input {
      border: none;
    }

    .d-flex {
      &.align-items-center.justify-content-start {
        flex: 1; /* Take up remaining space */
      }

      &.align-items-center.justify-content-end {
      }
    }
  }

  .add-new-data-btn {
    border-radius: 0.5rem; // Match Figma
    color: #6c757d;
    width: 97.5%; // Adjust width to match parent
  }

  // Add styles for icon container
  .awe-icons {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer; // Ensure it's clickable

    &.align-items-center.justify-content-end {
      // Styles for icon container
    }
  }

  .regenerate-section {
    margin-right: 1rem;

    .form-label {
      font-size: 0.9rem;
    }
    .input-group {
      .btn-sm {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
      input.form-control-sm {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }
}

.edit-modal-footer {
  .update-btn {
    background-color: #adb5bd; // Light gray like Figma
    border-color: #adb5bd;
    color: #fff;
    border-radius: 20px; // Pill shape
    padding: 0.375rem 1.5rem;
    font-weight: 500;

    &:hover {
      background-color: #9fa6ac;
      border-color: #9fa6ac;
    }
  }
}

// Overided propety
:host ::ng-deep .input-container {
  padding: 0.5rem 0 2rem;
  .input-wrapper {
    height: 2.25rem;
    border-radius: 0.5rem;
    border: 0.7px solid var(--product-stroke, #4244c2);
  }
}

:host ::ng-deep button.hover-slide-bg {
  border-radius: 25px;
}
