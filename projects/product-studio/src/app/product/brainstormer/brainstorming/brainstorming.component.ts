import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { trigger, state, style, transition, animate } from '@angular/animations';

import { BrainstormerStepperComponent } from "../components/stepper/stepper.component";
import { HeadingComponent, IconsComponent } from '@awe/play-comp-library';
import { StepperService, StepperStep } from '../../shared/services/stepper-service/stepper.service';

// Import all page components
import { UnderstandingComponent } from '../pages/understanding/understanding.component';
import { UserPersonaComponent } from '../pages/user-persona/user-persona.component';
import { PersonaDetailsComponent } from '../pages/persona-details/persona-details.component';
import { FeatureListComponent } from '../pages/feature-list/feature-list.component';
import { SwotAnalysisComponent } from "../pages/swot-analysis/swot-analysis.component";
import { ProductRoadmapComponent } from "../pages/product-roadmap/product-roadmap.component";
import { SplitScreenComponent } from '../components/split-screen/split-screen.component';
import { ChatPanelComponent } from '../components/chat-panel/chat-panel.component';

@Component({
  selector: 'app-brainstorming',
  imports: [
    CommonModule,
    BrainstormerStepperComponent,
    UnderstandingComponent,
    UserPersonaComponent,
    PersonaDetailsComponent,
    FeatureListComponent,
    SwotAnalysisComponent,
    ProductRoadmapComponent,
    IconsComponent,
    HeadingComponent,
    SplitScreenComponent,
    ChatPanelComponent
],
  templateUrl: './brainstorming.component.html',
  styleUrl: './brainstorming.component.scss',
  animations: [
    trigger('roboBallAnimation', [
      state('idle', style({
        transform: 'scale(1) rotate(0deg)',
        boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.15)'
      })),
      state('hover', style({
        transform: 'scale(1.1) rotate(5deg)',
        boxShadow: '0px 8px 25px rgba(74, 144, 226, 0.3)'
      })),
      state('clicked', style({
        transform: 'scale(0.95) rotate(-5deg)',
        boxShadow: '0px 4px 15px rgba(74, 144, 226, 0.5)'
      })),
      transition('idle => hover', animate('0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)')),
      transition('hover => idle', animate('0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)')),
      transition('* => clicked', animate('0.15s ease-in')),
      transition('clicked => *', animate('0.3s ease-out'))
    ]),
    trigger('promptAnimation', [
      state('hidden', style({
        opacity: 0,
        transform: 'translateX(-20px) scale(0.9)'
      })),
      state('visible', style({
        opacity: 1,
        transform: 'translateX(0) scale(1)'
      })),
      transition('hidden => visible', animate('0.4s 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94)')),
      transition('visible => hidden', animate('0.2s ease-in'))
    ])
  ]
})
export class BrainstormingComponent implements OnInit, OnDestroy {
  roboBallIcon: string = '/icons/robo_ball.svg';

  currentStep: StepperStep | null = null;
  currentStepIndex: number = 0;
  canGoNext: boolean = false;
  canGoPrevious: boolean = false;
  // State for left panel toggle
  leftPanelWidth: number = 35; // Default width for left panel
  showSplitScreen = false;
  isDarkMode = false;

  // Animation states
  roboBallState = 'idle';
  promptState = 'visible';

  // Persona details state
  showPersonaDetails = false;
  selectedPersonaId: string | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    private stepperService: StepperService
  ) {}

  ngOnInit(): void {
    // Subscribe to current step changes
    this.subscriptions.push(
      this.stepperService.currentStep$.subscribe(step => {
        this.currentStep = step;
        this.updateNavigationState();
      })
    );

    // Subscribe to current step index changes
    this.subscriptions.push(
      this.stepperService.currentStepIndex$.subscribe(index => {
        this.currentStepIndex = index;
        this.updateNavigationState();
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onStepChanged(event: { step: StepperStep; index: number }): void {
    // Handle step change if needed
    console.log('Step changed:', event);
  }

  nextStep(): void {
    if (this.stepperService.nextStep()) {
      // Mark current step as completed when moving to next
      this.stepperService.markStepAsCompleted(this.currentStepIndex - 1);
    }
  }

  previousStep(): void {
    this.stepperService.previousStep();
  }

  private updateNavigationState(): void {
    this.canGoNext = this.stepperService.canGoNext();
    this.canGoPrevious = this.stepperService.canGoPrevious();
  }

  toggleLeftPanel(): void {
    // Implement left panel toggle logic
  }

  // Helper method to get current component name for rendering
  getCurrentComponent(): string {
    return this.currentStep?.component || 'understanding';
  }

  // Helper method to check if a specific component should be shown
  shouldShowComponent(componentName: string): boolean {
    return this.getCurrentComponent() === componentName;
  }

  openSplitScreen() {
    this.roboBallState = 'clicked';
    // Don't hide the prompt anymore - keep it visible
    // this.promptState = 'hidden';

    setTimeout(() => {
      this.showSplitScreen = true;
      this.roboBallState = 'idle';
    }, 200);
  }

  toggleSplitScreen() {
    if (this.showSplitScreen) {
      this.closeSplitScreen();
    } else {
      this.openSplitScreen();
    }
  }

  closeSplitScreen() {
    this.showSplitScreen = false;
    this.roboBallState = 'idle';
  }

  onRoboBallHover() {
    if (this.roboBallState === 'idle') {
      this.roboBallState = 'hover';
    }
  }

  onRoboBallLeave() {
    if (this.roboBallState === 'hover') {
      this.roboBallState = 'idle';
    }
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
  }

  onSplitScreenClose() {
    // Keep the prompt visible - don't change its state
    console.log('Split screen closed');
  }

  onSplitScreenOpen() {
    // Keep the prompt visible - don't change its state
    console.log('Split screen opened');
  }

  // --- Persona Details Methods ---
  showPersonaDetailsView(personaId: string): void {
    this.selectedPersonaId = personaId;
    this.showPersonaDetails = true;
  }

  hidePersonaDetailsView(): void {
    this.showPersonaDetails = false;
    this.selectedPersonaId = null;
  }

  // Helper method to check if persona details should be shown
  shouldShowPersonaDetails(): boolean {
    return this.shouldShowComponent('persona') && this.showPersonaDetails;
  }

  // Helper method to check if persona list should be shown
  shouldShowPersonaList(): boolean {
    return this.shouldShowComponent('persona') && !this.showPersonaDetails;
  }
}
