// Chat Panel Styles
.chat-panel {
  display: flex;
  height: 65vh;
  flex-direction: column;
  position: relative;
  animation: slideInLeft 0.5s ease-out;
  flex-shrink: 0;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0px 0px 20px 0px rgba(151, 151, 151, 0.25);

  // Ensure the panel takes full height and positions input at bottom
  justify-content: space-between;
}

// Chat header styling
.chat-header {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px 12px 0 0;

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
  }

  h3 {
    margin: 0;
    color: white;
    font-size: 16px;
    font-weight: 600;
  }
}

// Chat content area
.chat-content {
  flex: 1; // Take all available space above the input
  overflow-y: auto; // Allow scrolling for chat messages
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }
}

// Welcome message styling
.welcome-message {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;

  p {
    margin: 0;
    color: #4a5568;
    font-size: 14px;
    line-height: 1.5;
    text-align: center;
  }
}

// Messages container
.messages-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-bottom: 8px;
}

// Message wrapper for positioning
.message-wrapper {
  display: flex;
  width: 100%;

  &.user-message {
    justify-content: flex-end; // User messages on the right

    .message-bubble {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 18px 18px 4px 18px;
      max-width: 80%;
      margin-left: auto;

      &::after {
        content: "";
        position: absolute;
        bottom: -1px;
        right: 6px;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #764ba2;
      }
    }
  }

  &.ai-message {
    justify-content: flex-start; // AI messages on the left

    .message-bubble {
      background: #f7f8fc;
      color: #2d3748;
      border: 1px solid #e2e8f0;
      border-radius: 18px 18px 18px 4px;
      max-width: 80%;
      margin-right: auto;

      &::after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: 6px;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #f7f8fc;
      }
    }
  }
}

// Message bubble styling
.message-bubble {
  padding: 12px 16px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: messageSlideIn 0.3s ease-out;

  p {
    margin: 0 0 4px 0;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
  }

  .message-time {
    font-size: 11px;
    opacity: 0.7;
    display: block;
    text-align: right;
    margin-top: 4px;
  }
}

// Typing indicator
.typing-indicator {
  background: #f7f8fc !important;
  border: 1px solid #e2e8f0 !important;
  padding: 16px !important;

  .typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;

    span {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #a0aec0;
      animation: typingDot 1.4s infinite ease-in-out;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

// Chat input container
.chat-input-container {
  // padding: 16px;
  // border-top: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  // gap: 8px;

  // Remove absolute positioning - use flexbox instead
  flex-shrink: 0; // Don't shrink when panel content grows
  width: 100%; // Take full width of the panel
  box-sizing: border-box; // Include padding in width calculation

  // Ensure proper spacing and alignment
  justify-content: start;

  // Add background to distinguish from chat content
  background: #fff;
  border-radius: 0 0 12px 12px; // Match panel border radius
}

// AI Assistant Icon styling
.ai-assistant-icon {
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
  margin-right: 12px;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0px 8px 25px rgba(74, 144, 226, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }

  img {
    width: 47px;
    height: 47px;
    object-fit: contain;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: rotate(5deg) scale(1.1);
  }
}

// Animations
@keyframes slideInLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes messageSlideIn {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes typingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}