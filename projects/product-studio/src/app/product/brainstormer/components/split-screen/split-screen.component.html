<div
  class="split-screen-overlay"
  [class.active]="isOpen"
  [@slideAnimation]="isOpen ? 'open' : 'closed'"
  [@backdropAnimation]="isOpen ? 'open' : 'closed'"
  [attr.data-theme]="theme"
>

  <div class="split-container">
    <!-- Left Panel (Chat) -->
    <div
      class="left-panel"
      [style.width.%]="leftPanelWidth"
      [class.light]="theme === 'light'"
      [class.dark]="theme === 'dark'"
      [@panelAnimation]
    >
      <ng-content select="[slot=left-panel]"></ng-content>
    </div>

    <!-- Resize Handle -->
    <div
      class="resize-handle"
      (mousedown)="startResize($event)"
      [class.light]="theme === 'light'"
      [class.dark]="theme === 'dark'"
    >
      <div class="resize-indicator"></div>
    </div>

    <!-- Right Panel (Main Content) -->
    <div
      class="right-panel"
      [style.width.%]="100 - leftPanelWidth"
      [class.light]="theme === 'light'"
      [class.dark]="theme === 'dark'"
      [@panelAnimation]
    >
      <ng-content select="[slot=right-panel]"></ng-content>
    </div>
  </div>
</div>
