// Container & Background
.brainstormer-container {
  background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
  min-height: 100vh;
}

// Card Styling
.brainstormer-card {
  --awe-card-border: none;
  --awe-card-box-shadow: 0 8px 32px rgba(138, 63, 252, 0.1);
  --awe-card-border-radius: 20px;
  max-width: 820px;
  background: white;
}

// Header Typography
.heading {
  font-size: 2rem;
  font-weight: 600;
  color: #212529;
  
  @media (max-width: 576px) {
    font-size: 1.5rem;
  }
}

.brand-text {
  background: linear-gradient(90deg, #8A3FFC 0%, #E5369A 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.subtitle {
  font-size: 1rem;
  
  @media (max-width: 576px) {
    font-size: 0.9rem;
  }
}

// Form Elements
.input-field {
  background-color: #f8f9fa;
  border: 1px solid transparent;
  border-radius: 8px;
  height: 48px;
  transition: all 0.2s ease;
  
  &:focus {
    background-color: white;
    border-color: #8A3FFC;
    box-shadow: 0 0 0 0.2rem rgba(138, 63, 252, 0.15);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
}

.input-icon, .select-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #8A3FFC;
  pointer-events: none;
  z-index: 2;
  
  img {
    filter: brightness(0) saturate(100%) invert(48%) sepia(79%) saturate(2476%) hue-rotate(258deg) brightness(98%) contrast(101%);
  }
}

.select-icon {
  right: 2.5rem; // Account for dropdown arrow
}

// User Group Buttons
.user-group-btn {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 24px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #8A3FFC;
    color: #8A3FFC;
  }
  
  &.selected {
    background: #f3f0ff;
    border-color: #8A3FFC;
    color: #8A3FFC;
  }
}

.btn-add-more {
  background: #f3f0ff;
  border: 1px solid #e9e8ec;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  padding: 0;
  color: #8A3FFC;
  transition: all 0.2s ease;
  
  &:hover {
    background: #8A3FFC;
    color: white;
  }
}

// Submit Button
.btn-submit {
  background: #8A3FFC;
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  min-width: 120px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: #7c36e8;
  }
}

// Responsive Typography
@media (max-width: 768px) {
  .form-label {
    font-size: 0.875rem;
  }
  
  .user-group-btn {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
}