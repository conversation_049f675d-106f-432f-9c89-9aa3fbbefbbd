<div class="brainstormer-container d-flex align-items-center justify-content-center min-vh-100 p-3">
  
  <awe-card class="brainstormer-card w-100" [applyBodyPadding]="false" [applyHeaderPadding]="false">
    
    <!-- Header Section -->
    <div awe-card-header-content class="px-4 px-md-5 py-4 py-md-5 pb-3 text-center">
      <h2 class="heading d-flex align-items-center justify-content-center mb-2">
        <!-- Sparkle Icon -->
        <img [src]="sparkleIcon" alt="Sparkle" class="sparkle-icon me-2 flex-shrink-0" width="24" height="24">
        Shape your vision with <span class="brand-text ms-1">Brainstormer</span>
      </h2>
      <p class="subtitle text-muted mb-0">Let's craft the next big thing - together.</p>
    </div>

    <!-- Form Body -->
    <div class="px-4 px-md-5 pb-4 pb-md-5">
      <form (ngSubmit)="onSubmitRecipeForm()" #form="ngForm">
        
        <!-- Project Name & Industry Row -->
        <div class="row g-3 g-md-4 mb-4">
          <div class="col-12 col-md-6">
            <label class="form-label fw-medium">Your project</label>
            <div class="position-relative">
              <input 
                type="text" 
                class="form-control input-field pe-5" 
                placeholder="Project Name"
                [(ngModel)]="projectName" 
                name="projectName">
              <div class="input-icon">
                <img [src]="sparkleIcon" alt="Sparkle" width="16" height="16">
              </div>
            </div>
          </div>
          
          <div class="col-12 col-md-6">
            <label class="form-label fw-medium">Industry</label>
            <div class="position-relative">
              <select 
                class="form-select input-field" 
                [(ngModel)]="industry" 
                name="industry">
                <option *ngFor="let ind of industries" [value]="ind">{{ ind }}</option>
              </select>
            </div>
          </div>
        </div>

        <!-- User Groups Section -->
        <div class="mb-5">
          <label class="form-label fw-medium">User Groups</label>
          <div class="d-flex flex-wrap align-items-center gap-2 mt-2">
            <button 
              *ngFor="let group of userGroups; let i = index"
              type="button" 
              class="btn user-group-btn"
              [class.selected]="group.selected"
              (click)="toggleGroup(i)">
              {{ group.label }}
            </button>
            
            <!-- Add More Button -->
            <button type="button" class="btn btn-add-more" title="Add more user groups">
              <img [src]="addIcon" alt="Add" width="36" height="36">
            </button>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="text-center">
          <button type="submit" class="btn btn-submit px-4 py-2">
            Submit
          </button>
        </div>
        
      </form>
    </div>
  </awe-card>
</div>