:host {
  display: block;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;

  // --- CSS Custom Properties for Theming ---
  --awe-card-padding-base: 0 0.75rem ;
  --awe-card-border-radius: 1rem;
  --awe-card-border: 1px solid #dedddd;
  --awe-card-background: var(--Neutral-N--50, #fff);
  --awe-card-box-shadow: 0px 0px 16px 0px rgba(225, 225, 225, 0.25);
  --awe-card-box-shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);

  // Section specific padding variables
  --awe-card-header-padding: var(--awe-card-padding-base);
  --awe-card-body-padding: var(--awe-card-padding-base);
  --awe-card-footer-padding: var(--awe-card-padding-base);

  // Body specific (example if different from base)
  // --awe-card-body-font-size: 0.8rem;
}

.awe-card-wrapper {
  border-radius: var(--awe-card-border-radius);
  border: var(--awe-card-border);
  background: var(--awe-card-background);
  box-shadow: var(--awe-card-box-shadow);
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s ease;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &:hover {
    box-shadow: var(--awe-card-box-shadow-hover);
  }
}
::ng-deep awe-heading{
  width:60
}

.awe-card-header {
  // Basic container styling. The content and its layout are projected.
  // Optional: border-bottom: var(--awe-card-border);

  // Apply padding if the input flag is true
  .awe-card-wrapper.with-header-padding & {
    padding: var(--awe-card-header-padding);
  }
  // Consumer will style the elements they project into this header.
}

.awe-card-body {
  flex-grow: 1;
  overflow-y: auto;
  // font-size: var(--awe-card-body-font-size);

  .awe-card-wrapper.with-body-padding & {
    padding: var(--awe-card-body-padding);
    padding-top: 0; // Remove top padding from card body
    padding-bottom: 15px; // Remove bottom padding from card body
  }
}

.awe-card-footer {
  // Optional: border-top: var(--awe-card-border);

  .awe-card-wrapper.with-footer-padding & {
    padding: var(--awe-card-footer-padding);
  }
}