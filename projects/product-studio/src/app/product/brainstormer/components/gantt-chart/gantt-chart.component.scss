.gantt-chart {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.gantt-header {
  display: flex;
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
}

.task-column {
  width: 200px;
  min-width: 200px;
  border-right: 1px solid #e2e8f0;
}

.timeline-header {
  flex: 1;

  .quarters-row {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    height: 92px;

    .quarter-header {
      padding: 24px 84px;
      text-align: center;
      border: 1px solid #d6d6d6;
      background: #fff;
    }
  }

  .months-row {
    display: flex;
    border: 1px solid #e2e8f0;

    .month-header {
      padding: 4px 4px;
      text-align: center;
      font-size: 11px;
      font-weight: 500;
      color: #6b7280;
      border-right: 1px solid #f1f5f9;

      &.current-month {
        &::after {
          content: "";
          position: absolute;
          bottom: -2px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 4px solid transparent;
          border-right: 4px solid transparent;
          border-top: 4px solid #10b981;
        }
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.gantt-body {
  position: relative; // Important for absolute positioning
  border-right: 1px solid #f1f5f9;
  min-height: 516px;

  .gantt-row {
    display: flex;
    // border-right: 1px solid #f1f5f9;
    min-height: 70px;

    &:hover {
      background: #f8fafc;
    }

    .task-name {
      width: 200px;
      min-width: 200px;
      padding: 12px 16px;
      display: flex;
      align-items: center;
      border-right: 1px solid #e2e8f0;
      font-size: 14px;
      color: #374151;
    }

    .timeline-container {
      flex: 1;
      padding: 6px 0;
      position: relative;

      .timeline-track {
        height: 34px;
        position: relative;

        .task-bar {
          position: relative;
          height: 52px;
          top: 5px;
          transition: all 0.2s ease;
          cursor: pointer;
          border-radius: 8px;
          background: #eeeff0;

          .task-indicator {
            // width: 10px;
            // height: 10px;
            border-radius: 50%;
            background: transparent;
          }

          .custom-tooltip {
            display: none;
            position: absolute;
            left: 50%;
            top: -38px;
            transform: translateX(-50%);
            background: #222;
            color: #fff;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: pre-line;
            z-index: 10;
            pointer-events: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
          &:hover .custom-tooltip {
            display: block;
          }
        }
      }
    }
  }
}
.tracking-line-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background: transparent;
  border-left: 2px dotted #10b981;
  pointer-events: none;
  border-radius: 28px;
  z-index: 10;

  .tracking-date-label {
    position: absolute;
    bottom: -25px; // Position below the gantt body
    left: 50%;
    transform: translateX(-50%);
    background: #10b981;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
  }
}

.grid-lines-container {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0; // Start from beginning since we want full width coverage
  right: 0;
  pointer-events: none;
  z-index: 1;
}

.month-divider {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 0;
  border-left: 1px solid #e5e7eb;
  opacity: 0.6;
}

// Ensure task content appears above grid lines
.gantt-row {
  position: relative;
  z-index: 2;
}
// Responsive design
@media (max-width: 768px) {
  .task-column {
    width: 150px;
    min-width: 150px;
  }

  .gantt-row .task-name {
    width: 150px;
    min-width: 150px;
    padding: 12px;
    font-size: 12px;
  }

  .timeline-header {
    .quarter-header {
      font-size: 12px;
      padding: 8px 4px;
    }

    .month-header {
      font-size: 10px;
      padding: 6px 2px;
    }
  }
}
