<div class="gantt-chart">
  <!-- Header with Quarters -->
  <div class="gantt-header">
    <div class="timeline-header">
      <div class="quarters-row">
        <div
          *ngFor="let quarter of quarters"
          class="quarter-header d-flex align-items-center justify-content-center"
          [style.width]="getQuarterWidth()"
        >
          <awe-heading variant="s2" type="bold">Quarter {{ quarter.quarter }} ({{ quarter.year }})</awe-heading>
          
        </div>
      </div>
    </div>
  </div>

  <!-- Tasks Grid -->
  <div
    class="gantt-body mt-5"
    (mousemove)="onTimelineMouseMove($event)"
    (mouseleave)="onTimelineMouseLeave()"
  >
    <div class="grid-lines-container">
      <div class="grid-lines-container">
        <div
          *ngFor="let position of getMonthDividerPositions()"
          class="month-divider"
          [style.left]="position"
        ></div>
      </div>
    </div>

    <div *ngFor="let task of tasks" class="gantt-row">
      <div class="timeline-container">
        <div class="timeline-track">
          <div
            class="task-bar d-flex justify-content-start align-items-center position-relative"
            [style.left]="getTaskPosition(task).left"
            [style.width]="getTaskPosition(task).width"
            [style.border-top]="`4px solid ${task.color}`"
          >
            <span
              class="task-indicator p-2 m-3"
              [style.border]="`6px solid ${task.color}`"
            ></span>
            <awe-heading
              *ngIf="shouldShowTaskName(task)"
              class="p-2 d-flex justify-content-start align-items-center"
              variant="s2"
              type="bold"
              >{{ task.name }}</awe-heading
            >
            <span class="custom-tooltip">
              {{ getTaskTooltip(task) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div
      *ngIf="showTrackingLine"
      class="tracking-line-overlay"
      [style.left]="trackingLinePosition"
    >
      <div class="tracking-date-label">{{ trackingDate }}</div>
    </div>
  </div>

  <div class="timeline-header">
    <div class="months-row">
      <div
        *ngFor="let month of months"
        class="month-header"
        [class.current-month]="isCurrentMonth(month)"
        [style.width]="getMonthWidth()"
      >
        {{ month }}
      </div>
    </div>
  </div>
</div>
