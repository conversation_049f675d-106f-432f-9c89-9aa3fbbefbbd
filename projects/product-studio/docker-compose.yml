# =============================================================================
# Docker Compose Configuration for Product Studio Application
# =============================================================================
# 
# This file defines the services for running the Product Studio application
# in both production and development environments.
# =============================================================================

version: '3.8'

# =============================================================================
# Services Configuration
# =============================================================================
services:
  # =====================================================================
  # Production Service
  # =====================================================================
  product-studio:
    build:
      context: ../..
      dockerfile: projects/product-studio/Dockerfile
      target: production
    container_name: product-studio-app
    ports:
      - "8082:8080"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - product-studio-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.product-studio.rule=Host(`product-studio.localhost`)"
      - "traefik.http.services.product-studio.loadbalancer.server.port=8080"

  # =====================================================================
  # Development Service (Optional)
  # =====================================================================
  product-studio-dev:
    build:
      context: ../..
      dockerfile: projects/product-studio/Dockerfile.dev
      target: development
    container_name: product-studio-dev
    ports:
      - "4202:4202"
    environment:
      - NODE_ENV=development
    volumes:
      - ../../:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - product-studio-network
    profiles:
      - dev

# =============================================================================
# Networks Configuration
# =============================================================================
networks:
  product-studio-network:
    driver: bridge 