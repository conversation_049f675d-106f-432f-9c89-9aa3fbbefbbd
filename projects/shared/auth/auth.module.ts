import { NgModule, Provider } from '@angular/core';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { AuthService } from './auth.service';
import { AuthTokenService } from './auth-token.service';
import { TokenStorageService } from './token-storage.service';
import { AuthGuard } from './auth.guard';
import { AuthInterceptor } from './auth.interceptor';

const AUTH_PROVIDERS: Provider[] = [
  AuthService,
  AuthTokenService,
  TokenStorageService,
  AuthGuard,
  { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
];

@NgModule({
  providers: AUTH_PROVIDERS,
})
export class AuthModule {} 