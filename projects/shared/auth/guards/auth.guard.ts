import { Injectable, inject } from '@angular/core';
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, take, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { TokenStorageService } from '../services/token-storage.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);
  private readonly tokenStorage = inject(TokenStorageService);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> {
    const accessToken = this.tokenStorage.getAccessToken();
    const orgPath = this.tokenStorage.getCookie('org_path');
    const url = state.url;
    const requireOrgPath = route.data && route.data['requireOrgPath'];
    if (!accessToken) {
      return of(this.router.createUrlTree(['/login']));
    }
    if (requireOrgPath) {
      if (accessToken && !orgPath && url !== '/org-config') {
        return of(this.router.createUrlTree(['/org-config']));
      }
      if (accessToken && orgPath && (url === '/login' || url === '/org-config')) {
        return of(this.router.createUrlTree(['/dashboard']));
      }
    }
    return of(true);
  }

  private handleSSOAuth(): Observable<boolean | UrlTree> {
    const hasAccessToken = !!this.tokenStorage.getAccessToken();
    if (!hasAccessToken) {
      console.info('No access token found, redirecting to login');
      return of(this.router.createUrlTree(['/login']));
    }
    return of(true);
  }

  private handleBasicLoginAuth(): Observable<boolean | UrlTree> {
    const hasAccessToken = !!this.tokenStorage.getAccessToken();
    if (!hasAccessToken) {
      console.info('No access token found, redirecting to login');
      return of(this.router.createUrlTree(['/login']));
    }
    return of(true);
  }
} 