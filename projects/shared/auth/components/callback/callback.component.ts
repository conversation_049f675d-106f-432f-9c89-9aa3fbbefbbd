import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-callback',
  template: '',
})
export class CallbackComponent implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
  ) {}

  ngOnInit(): void {
    const refreshToken = this.route.snapshot.queryParams['refresh_token'];
    const code = this.route.snapshot.queryParams['code'];
    const error = this.route.snapshot.queryParams['error'];

    if (error) {
      console.error('Azure AD returned an error:', error);
      this.router.navigate(['/login'], {
        state: {
          error: `Azure AD login failed: ${this.route.snapshot.queryParams['error_description'] || error}`,
        },
      });
      return;
    }

    if (refreshToken) {
      this.handleTokenRefresh(refreshToken);
    } else if (code) {
      console.log('Authorization code received, length:', code.length);
      this.handleCodeExchange(code);
    } else {
      console.warn(
        'No authorization code or refresh token found in callback URL',
      );
    }
  }

  private handleTokenRefresh(refreshToken: string): void {
    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({
      next: () => {
        const redirectUrl = this.authService.getPostLoginRedirectUrl();
        this.router.navigate([redirectUrl]);
      },
      error: (err) => {
        console.error('Token refresh failed:', err);
        this.router.navigate(['/login']);
      },
    });
    this.subscription.add(refreshSub);
  }

  private handleCodeExchange(code: string): void {
    const exchangeSub = this.authService
      .exchangeCodeForToken(code)
      .subscribe({
        next: () => {
          const redirectUrl = this.authService.getPostLoginRedirectUrl();
          this.router.navigate([redirectUrl]);
          console.log('Token exchange successful');
        },
        error: (err) => {
          console.error('Token exchange failed:', err);
          this.router.navigate(['/login']);
        },
      });
    this.subscription.add(exchangeSub);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
