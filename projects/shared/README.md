# Shared Library

This library contains all shared code for the monorepo. Add new shared code to the appropriate folder. Import from `@shared` in any micro app.

## Structure

- `auth/` — Authentication logic
- `components/` — UI components
- `directives/` — Angular directives
- `pipes/` — Angular pipes
- `services/` — General services
- `utils/` — Helper functions, constants, types

## Usage

```ts
import { ButtonComponent } from '@shared/components/button/button.component';
import { AuthService } from '@shared/auth/auth.service';
```

## How to Add New Shared Code

- **UI Component?** → Add to `components/`
- **Directive?** → Add to `directives/`
- **Pipe?** → Add to `pipes/`
- **General Service?** → Add to `services/`
- **Auth-related?** → Add to `auth/`
- **Utility function or constant?** → Add to `utils/`

## Example: Adding a Shared Button

**File:** `projects/shared/components/button/button.component.ts`
```ts
import { Component, Input } from '@angular/core';

@Component({
  selector: 'shared-button',
  template: `<button [type]="type" [ngClass]="variant">{{ label }}</button>`,
  styleUrls: ['./button.component.scss']
})
export class ButtonComponent {
  @Input() label = '';
  @Input() type: 'button' | 'submit' = 'button';
  @Input() variant: 'primary' | 'secondary' = 'primary';
}
```

**Export in `index.ts`:**
```ts
export * from './components/button/button.component';
```

---

**This structure is scalable, clear, and easy for any team to use and grow.** 