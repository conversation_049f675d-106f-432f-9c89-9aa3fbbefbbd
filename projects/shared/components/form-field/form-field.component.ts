import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

export interface SelectOption {
  value: string | number;
  label: string;
}

@Component({
  selector: 'app-form-field',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './form-field.component.html',
  styleUrls: ['./form-field.component.scss']
})
export class FormFieldComponent implements OnInit {
  @Input() label: string = '';
  @Input() placeholder: string = '';
  @Input() control!: FormControl;
  @Input() type: 'text' | 'textarea' | 'number' | 'email' | 'password' | 'select' | 'checkbox' = 'text';
  @Input() id: string = '';
  @Input() options: SelectOption[] = [];
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() showAsterisk: boolean = true;
  @Input() hint: string = '';
  @Input() min: number | null = null;
  @Input() max: number | null = null;
  @Input() maxlength: number | null = null;
  @Input() showAttachIcon: boolean = false;
  @Input() attachIconTooltip: string = 'Attach file';
  @Output() attachClick = new EventEmitter<void>();
  @Input() iconSrc: string = ''; // Path to icon image
  @Input() iconTooltip: string = '';
  @Input() iconSize: { width: string; height: string } = { width: '16px', height: '16px' };
  @Output() iconClicked = new EventEmitter<void>();

  constructor() { }

  ngOnInit(): void {
    if (!this.id) {
      this.id = 'form-field-' + Math.random().toString(36).substring(2, 9);
    }
  }

  /**
   * Generate error message based on validation errors
   */
  getErrorMessage(): string {
    if (!this.control || !this.control.errors) {
      return '';
    }

    const errors = this.control.errors;

    if (errors['required']) {
      return 'This field is required';
    }
    if (errors['email']) {
      return 'Please enter a valid email address';
    }
    if (errors['minlength']) {
      return `Minimum length is ${errors['minlength'].requiredLength} characters`;
    }
    if (errors['maxlength']) {
      return `Maximum length is ${errors['maxlength'].requiredLength} characters`;
    }
    if (errors['min']) {
      return `Minimum value is ${errors['min'].min}`;
    }
    if (errors['max']) {
      return `Maximum value is ${errors['max'].max}`;
    }
    if (errors['pattern']) {
      return 'Please enter a valid format';
    }

    return 'Invalid input';
  }

  onAttachClick(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.attachClick.emit();
  }

  onIconClick(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.iconClicked.emit();
  }
} 