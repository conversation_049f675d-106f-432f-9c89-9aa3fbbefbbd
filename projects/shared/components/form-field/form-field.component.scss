.form-field {
  margin-bottom: 16px;
  width: 100%;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--form-label-color);
  font-size: 14px;
  transition: color 0.3s ease;

    .required-asterisk {
    color: var(--error-color, #e74c3c);
    margin-left: 4px;
    font-weight: 600;
  }
}

.form-input, .form-textarea, select.form-input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--form-input-border);
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  color: var(--form-input-color);
  background-color: var(--form-input-bg);
  transition: all 0.3s ease;

  // Disable browser default password visibility toggle
  &::-ms-reveal,
  &::-ms-clear {
    display: none;
  }

  &::-webkit-credentials-auto-fill-button,
  &::-webkit-strong-password-auto-fill-button {
    display: none !important;
  }

  &:focus {
    outline: none;
    border-color: var(--form-input-focus-border);
    box-shadow: 0 0 0 2px var(--form-input-focus-shadow);
  }

  &:hover:not(:focus) {
    border-color: var(--form-input-focus-border);
    box-shadow: 0 0 0 1px var(--form-input-focus-shadow);
  }

  &::placeholder {
    color: var(--form-input-placeholder);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    filter: grayscale(0.2);
  }
}

// Special styling for select dropdown
select.form-input {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12' fill='none'%3E%3Cpath d='M2.5 4L6 7.5L9.5 4' stroke='%23666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  padding-right: 40px;
  
  option {
    color: var(--input-text);
    background-color: var(--input-bg);
  }
}

// Checkbox styling
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
}

.form-checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  background-color: var(--input-bg);
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  
  &:checked {
    background-color: var(--primary-start);
    border-color: var(--primary-start);
    
    &::after {
      content: '';
      position: absolute;
      left: 6px;
      top: 3px;
      width: 5px;
      height: 9px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }
  
  &:focus {
    box-shadow: 0 0 0 2px var(--input-focus-shadow);
    outline: none;
  }
  
  &:hover:not(:checked) {
    border-color: var(--input-focus-border);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.checkbox-label {
  font-size: 14px;
  color: var(--text-color);
  cursor: pointer;
}

.form-textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--form-input-border);
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  color: var(--form-input-color);
  background-color: var(--form-input-bg);
  min-height: 100px;
  resize: vertical; /* Allow vertical resizing by default */
  line-height: 1.5;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: var(--form-input-focus-border);
    box-shadow: 0 0 0 2px var(--form-input-focus-shadow);
  }

  &:hover:not(:focus) {
    border-color: var(--form-input-focus-border);
    box-shadow: 0 0 0 1px var(--form-input-focus-shadow);
  }

  &::placeholder {
    color: var(--form-input-placeholder);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    filter: grayscale(0.2);
  }
}

.hint-text {
  margin-top: 6px;
  font-size: 12px;
  color: var(--text-secondary);
  opacity: 0.9;
}

.error-message {
  margin-top: 6px;
  color: var(--error-color);
  font-size: 12px;
  font-weight: 500;
  animation: fadeIn 0.3s ease;
}

// Theme transition
.form-input, .form-textarea, select.form-input, .form-checkbox, .form-label {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .form-input, .form-textarea, select.form-input {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .form-label, .checkbox-label {
    font-size: 13px;
  }
  
  .form-checkbox {
    width: 16px;
    height: 16px;
    
    &:checked::after {
      left: 5px;
      top: 2px;
      width: 4px;
      height: 8px;
    }
  }
}

/* Textarea container styling */
.textarea-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* Form textarea with icon */
.textarea-with-icon {
  padding-right: 40px !important; /* Space for the icon */
}

/* Attach icon styling */
.attach-icon {
  position: absolute;
  right: 12px;
  bottom: auto; /* Remove bottom positioning */
  top: 50%; /* Position in the vertical center */
  transform: translateY(-50%); /* Center exactly */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  background-color: transparent;
  color: var(--text-secondary, #666);
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 2;
  
  &:hover {
    color: var(--primary-color, #5b5fc7);
    background-color: var(--card-hover-shadow, rgba(0, 0, 0, 0.05));
  }
  
  svg {
    stroke: currentColor;
  }
}

/* Input container and right icon styles */
.input-container {
  position: relative !important;
  width: 100%;
  display: block;
}

.input-with-icon {
  padding-right: 40px !important;
}

.right-icon {
  position: absolute !important;
  right: 12px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  pointer-events: auto !important;
  z-index: 999 !important;
  cursor: pointer !important;
  width: 20px;
  height: 20px;

  img {
    display: block !important;
    max-width: none !important;
    max-height: none !important;
    pointer-events: none !important;
  }
}