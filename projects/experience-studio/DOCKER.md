# Docker Setup for Experience Studio

This document provides comprehensive instructions for dockerizing the Experience Studio Angular application with best practices.

## 🏗️ Architecture

The Docker setup uses a multi-stage build approach:

1. **Builder Stage**: Node.js environment for building the Angular application
2. **Production Stage**: Nginx server for serving the built application

## 📁 Files Structure

```
projects/experience-studio/
├── Dockerfile              # Production multi-stage build
├── Dockerfile.dev          # Development environment
├── docker-compose.yml      # Orchestration for development/production
├── nginx.conf             # Custom Nginx configuration
├── .dockerignore          # Excluded files from build context
├── docker-build.sh        # Build automation script
└── DOCKER.md              # This documentation
```

## 🚀 Quick Start

### Production Build

```bash
# Build the production image
./docker-build.sh v1.0.0

# Run with Docker Compose
docker-compose up -d

# Or run standalone
docker run -d -p 8081:8080 --name experience-studio-app experience-studio:latest
```

### Development Environment

```bash
# Run development environment
docker-compose --profile dev up -d

# Or build and run development container
docker build -f Dockerfile.dev -t experience-studio-dev .
docker run -d -p 4201:4201 -v $(pwd):/app experience-studio-dev
```

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `NODE_ENV` | `production` | Node.js environment |
| `PORT` | `8080` | Application port |

### Ports

- **8081**: Production application (Nginx) - mapped to container port 8080
- **4201**: Development server (Angular CLI)

## 🛡️ Security Features

### Security Headers
- X-Frame-Options: SAMEORIGIN
- X-Content-Type-Options: nosniff
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin
- Content-Security-Policy: Comprehensive CSP

### Container Security
- Non-root user (angular:1001)
- Minimal base images (Alpine Linux)
- Signal handling with dumb-init
- Health checks
- Rate limiting

## 📊 Performance Optimizations

### Nginx Configuration
- Gzip compression enabled
- Static asset caching (1 year)
- HTML files no-cache
- Module Federation remote entry caching
- Monaco Editor worker caching
- Optimized worker connections

### Docker Optimizations
- Multi-stage builds
- Layer caching optimization
- .dockerignore for reduced context
- BuildKit inline cache

## 🔍 Health Checks

The application includes health check endpoints:

```bash
# Health check
curl http://localhost:8081/health

# Docker health check
docker inspect experience-studio-app | grep Health -A 10
```

## 🐛 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
docker build --no-cache -f Dockerfile .
```

#### Port Conflicts
```bash
# Check port usage
lsof -i :8081

# Use different port
docker run -p 8082:8080 experience-studio:latest
```

#### Permission Issues
```bash
# Fix file permissions
sudo chown -R $USER:$USER .

# Run with proper user mapping
docker run -u $(id -u):$(id -g) experience-studio:latest
```

### Logs and Debugging

```bash
# View container logs
docker logs experience-studio-app

# Follow logs
docker logs -f experience-studio-app

# Execute commands in container
docker exec -it experience-studio-app sh
```

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
name: Build and Deploy

on:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker image
        run: |
          cd projects/experience-studio
          ./docker-build.sh ${{ github.sha }}
      - name: Push to registry
        run: |
          docker push your-registry.com/experience-studio:${{ github.sha }}
```

## 📈 Monitoring

### Metrics to Monitor
- Container health status
- Response times
- Error rates
- Resource usage (CPU, Memory)
- Disk I/O

### Log Aggregation
- Application logs: `/var/log/nginx/access.log`
- Error logs: `/var/log/nginx/error.log`
- Container logs: `docker logs experience-studio-app`

## 🔧 Customization

### Custom Nginx Configuration
Edit `nginx.conf` to modify:
- Security headers
- Caching policies
- Rate limiting
- Proxy settings

### Environment-Specific Builds
```bash
# Development build
docker build --target development -t experience-studio:dev .

# Production build with custom args
docker build --build-arg NODE_ENV=production -t experience-studio:prod .
```

## 📚 Best Practices

### Development
1. Use volume mounts for hot reloading
2. Keep development and production Dockerfiles separate
3. Use .dockerignore to exclude unnecessary files
4. Implement health checks

### Production
1. Use multi-stage builds
2. Run as non-root user
3. Implement proper logging
4. Use specific image tags
5. Regular security updates

### Security
1. Scan images for vulnerabilities
2. Keep base images updated
3. Minimize attack surface
4. Implement proper secrets management

## 🤝 Contributing

When contributing to the Docker setup:

1. Test builds locally
2. Update documentation
3. Follow security best practices
4. Add appropriate health checks
5. Consider backward compatibility

## 📞 Support

For issues related to the Docker setup:
1. Check the troubleshooting section
2. Review container logs
3. Verify configuration files
4. Test with minimal setup 