import { Injectable, signal } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
// import { HttpClient } from '@angular/common/http'; // Reserved for future HTTP requests

export interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  description?: string;
  editable: boolean;
}

export interface DesignTokensData {
  colors?: any[];
  typography?: any[];
  spacing?: any[];
  borders?: any[];
  shadows?: any[];
  tokens?: DesignToken[];
}

export interface DesignTokenEditState {
  isEditing: boolean;
  hasUnsavedChanges: boolean;
  originalValues: Map<string, string>;
  editingTokenId?: string;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowDesignTokenService {
  // private http = inject(HttpClient); // Reserved for future HTTP requests

  // Design token state management
  private designTokens$ = new BehaviorSubject<DesignToken[]>([]);
  private designSystemData$ = new BehaviorSubject<any>(null);
  private designTokenEditState$ = new BehaviorSubject<DesignTokenEditState>({
    isEditing: false,
    hasUnsavedChanges: false,
    originalValues: new Map()
  });

  // Signals for reactive state
  designTokensSignal = signal<DesignToken[]>([]);
  isEditingDesignTokens = signal<boolean>(false);

  constructor() {
    // Sync BehaviorSubject with signals
    this.designTokens$.subscribe(tokens => {
      this.designTokensSignal.set(tokens);
    });

    this.designTokenEditState$.subscribe(state => {
      this.isEditingDesignTokens.set(state.isEditing);
    });
  }

  // Public getters
  get designTokens(): Observable<DesignToken[]> {
    return this.designTokens$.asObservable();
  }

  get designTokensValue(): DesignToken[] {
    return this.designTokens$.value;
  }

  get designSystemData(): Observable<any> {
    return this.designSystemData$.asObservable();
  }

  get designSystemDataValue(): any {
    return this.designSystemData$.value;
  }

  get designTokenEditState(): Observable<DesignTokenEditState> {
    return this.designTokenEditState$.asObservable();
  }

  get designTokenEditStateValue(): DesignTokenEditState {
    return this.designTokenEditState$.value;
  }

  // Design token management
  updateDesignTokens(tokens: DesignToken[]): void {
    this.designTokens$.next(tokens);
  }

  updateDesignSystemData(data: any): void {
    this.designSystemData$.next(data);
  }

  addDesignToken(token: DesignToken): void {
    const currentTokens = this.designTokens$.value;
    this.designTokens$.next([...currentTokens, token]);
  }

  updateDesignToken(tokenId: string, updates: Partial<DesignToken>): void {
    const currentTokens = this.designTokens$.value;
    const updatedTokens = currentTokens.map(token =>
      token.id === tokenId ? { ...token, ...updates } : token
    );
    this.designTokens$.next(updatedTokens);
  }

  removeDesignToken(tokenId: string): void {
    const currentTokens = this.designTokens$.value;
    const filteredTokens = currentTokens.filter(token => token.id !== tokenId);
    this.designTokens$.next(filteredTokens);
  }

  findDesignTokenById(tokenId: string): DesignToken | null {
    return this.designTokens$.value.find(token => token.id === tokenId) || null;
  }

  // Design token processing and initialization
  processDesignTokens(tokensData: DesignTokensData): void {
    if (this.hasNewDesignTokenStructurePrivate(tokensData)) {
      this.initializeFromNewStructure(tokensData);
    } else {
      this.initializeFromLegacyStructure(tokensData);
    }
  }

  initializeDesignTokens(designSystemData?: any): void {
    if (designSystemData && this.hasNewDesignTokenStructurePrivate(designSystemData)) {
      this.initializeFromNewStructure(designSystemData);
    } else if (designSystemData) {
      // Handle legacy structure when data exists but is not new structure
      this.initializeFromLegacyStructure(designSystemData);
    } else {
      // No data provided, use defaults
      this.initializeDefaultTokens();
    }
  }

  hasNewDesignTokenStructure(data: any): boolean {
    try {
      return data?.colors && Array.isArray(data.colors) &&
             data.colors.length > 0 &&
             data.colors[0]?.id && data.colors[0]?.name && data.colors[0]?.value;
    } catch {
      return false;
    }
  }

  private hasNewDesignTokenStructurePrivate(data: any): boolean {
    return this.hasNewDesignTokenStructure(data);
  }

  private initializeFromNewStructure(data: any): void {
    try {
      const newTokens: DesignToken[] = [];

      // Process colors
      if (data.colors && Array.isArray(data.colors)) {
        data.colors.forEach((color: any, index: number) => {
          if (this.isValidArtifactDesignToken(color)) {
            newTokens.push({
              id: color.id || `color-${index}`,
              name: color.name,
              value: color.value,
              type: 'color',
              category: 'colors',
              description: color.description,
              editable: true
            });
          }
        });
      }

      // Process typography
      if (data.typography && Array.isArray(data.typography)) {
        data.typography.forEach((typo: any, index: number) => {
          if (this.isValidArtifactDesignToken(typo)) {
            newTokens.push({
              id: typo.id || `typography-${index}`,
              name: typo.name,
              value: typo.value,
              type: 'typography',
              category: 'typography',
              description: typo.description,
              editable: true
            });
          }
        });
      }

      this.updateDesignTokens(newTokens);
      this.updateDesignSystemData(data);
    } catch (error) {
      this.initializeDefaultTokens();
    }
  }

  private initializeFromLegacyStructure(data: any): void {
    // Handle legacy token structure
    const tokens: DesignToken[] = [];

    // Handle direct tokens array
    if (data.tokens && Array.isArray(data.tokens)) {
      tokens.push(...data.tokens);
    }
    // Handle DesignTokensData structure (colors, fonts, buttons)
    else if (data.colors && Array.isArray(data.colors)) {
      // Process colors from legacy structure
      data.colors.forEach((color: any, index: number) => {
        if (color.value && color.name) {
          tokens.push({
            id: `color-${index}`,
            name: color.name,
            value: color.value,
            type: 'color',
            category: 'Colors',
            description: `Color token: ${color.name}`,
            editable: true
          });
        }
      });

      // Process fonts if available
      if (data.fonts && Array.isArray(data.fonts)) {
        data.fonts.forEach((font: any, index: number) => {
          if (font.name) {
            tokens.push({
              id: `font-${index}`,
              name: font.name,
              value: `${font.weight || 'normal'}, ${font.size || '16px'}`,
              type: 'typography',
              category: 'Typography',
              description: `Font token: ${font.name}`,
              editable: true
            });
          }
        });
      }

      // Process buttons if available
      if (data.buttons && Array.isArray(data.buttons)) {
        data.buttons.forEach((button: any, index: number) => {
          if (button.name) {
            tokens.push({
              id: `button-${index}`,
              name: button.name,
              value: button.variant || 'default',
              type: 'size',
              category: 'Buttons',
              description: `Button token: ${button.name}`,
              editable: true
            });
          }
        });
      }
    }

    this.updateDesignTokens(tokens);
    this.updateDesignSystemData(data);
  }

  private initializeDefaultTokens(): void {
    const defaultTokens: DesignToken[] = [
      {
        id: 'primary-color',
        name: 'Primary Color',
        value: '#007bff',
        type: 'color',
        category: 'colors',
        description: 'Main brand color',
        editable: true
      },
      {
        id: 'secondary-color',
        name: 'Secondary Color',
        value: '#6c757d',
        type: 'color',
        category: 'colors',
        description: 'Secondary brand color',
        editable: true
      },
      {
        id: 'font-family',
        name: 'Font Family',
        value: 'Inter, sans-serif',
        type: 'typography',
        category: 'typography',
        description: 'Primary font family',
        editable: true
      }
    ];

    this.updateDesignTokens(defaultTokens);
  }

  isValidArtifactDesignToken(token: any): boolean {
    return token &&
           typeof token.id === 'string' && token.id.trim() !== '' &&
           typeof token.name === 'string' && token.name.trim() !== '' &&
           typeof token.value === 'string' && token.value.trim() !== '' &&
           typeof token.category === 'string' && token.category.trim() !== '' &&
           typeof token.editable === 'boolean';
  }

  // Design token editing
  enterDesignTokenEditMode(): void {
    const originalValues = new Map<string, string>();

    this.designTokens$.value.forEach(token => {
      originalValues.set(token.id, token.value);
    });

    this.designTokenEditState$.next({
      isEditing: true,
      hasUnsavedChanges: false,
      originalValues: originalValues
    });
  }

  exitDesignTokenEditMode(): void {
    this.designTokenEditState$.next({
      isEditing: false,
      hasUnsavedChanges: false,
      originalValues: new Map()
    });
  }

  updateTokenValue(tokenId: string, newValue: string): void {
    this.updateDesignToken(tokenId, { value: newValue });

    const currentState = this.designTokenEditState$.value;
    this.designTokenEditState$.next({
      ...currentState,
      hasUnsavedChanges: true
    });
  }

  saveDesignTokenChanges(): void {
    const currentState = this.designTokenEditState$.value;

    if (currentState.hasUnsavedChanges) {
      // Prepare tokens for API update
      const tokensPayload = this.prepareTokensForUpdate();
      this.sendTokensUpdateRequest(tokensPayload, null, null, null, null);
    }

    this.exitDesignTokenEditMode();
  }

  cancelDesignTokenChanges(): void {
    const currentState = this.designTokenEditState$.value;

    if (currentState.hasUnsavedChanges && currentState.originalValues.size > 0) {
      // Restore original values
      const restoredTokens = this.designTokens$.value.map(token => ({
        ...token,
        value: currentState.originalValues.get(token.id) || token.value
      }));

      this.updateDesignTokens(restoredTokens);
    }

    this.exitDesignTokenEditMode();
  }

  private prepareTokensForUpdate(): any {
    const tokens = this.designTokens$.value;
    const designSystemData = this.designSystemData$.value;

    return {
      colors: tokens
        .filter(token => token.type === 'color')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          description: token.description
        })),
      typography: tokens
        .filter(token => token.type === 'typography')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          description: token.description
        })),
      originalData: designSystemData
    };
  }



  // Token validation and structure
  validateTokenStructure(token: any): boolean {
    return token &&
           typeof token.id === 'string' && token.id.trim() !== '' &&
           typeof token.name === 'string' && token.name.trim() !== '' &&
           typeof token.value === 'string' && token.value.trim() !== '' &&
           typeof token.type === 'string' &&
           ['color', 'typography', 'spacing', 'size'].includes(token.type);
  }

  // Token export/import
  exportTokensAsCSS(): string {
    const tokens = this.designTokens$.value;
    let css = ':root {\n';

    tokens.forEach(token => {
      const cssVarName = `--${token.name.toLowerCase().replace(/\s+/g, '-')}`;
      css += `  ${cssVarName}: ${token.value};\n`;
    });

    css += '}\n';
    return css;
  }

  exportTokensAsJSON(): string {
    return JSON.stringify(this.designTokens$.value, null, 2);
  }

  importTokensFromFile(fileContent: string, format: 'json' | 'css'): boolean {
    try {
      if (format === 'json') {
        const tokens = JSON.parse(fileContent);
        if (Array.isArray(tokens) && tokens.every(token => this.validateTokenStructure(token))) {
          this.updateDesignTokens(tokens);
          return true;
        }
      } else if (format === 'css') {
        // Parse CSS custom properties
        const tokens = this.parseCSSTokens(fileContent);
        if (tokens.length > 0) {
          this.updateDesignTokens(tokens);
          return true;
        }
      }
      return false;
    } catch {
      return false;
    }
  }

  private parseCSSTokens(css: string): DesignToken[] {
    const tokens: DesignToken[] = [];
    const cssVarRegex = /--([^:]+):\s*([^;]+);/g;
    let match;

    while ((match = cssVarRegex.exec(css)) !== null) {
      const name = match[1].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      const value = match[2].trim();

      tokens.push({
        id: `imported-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        name: name,
        value: value,
        type: this.inferTokenType(value),
        category: 'imported',
        editable: true
      });
    }

    return tokens;
  }

  private inferTokenType(value: string): DesignToken['type'] {
    if (value.match(/^#[0-9a-fA-F]{3,8}$/) ||
        value.match(/^rgb\(/) ||
        value.match(/^rgba\(/) ||
        value.match(/^hsl\(/) ||
        value.match(/^hsla\(/)) {
      return 'color';
    }

    if (value.match(/\d+(px|em|rem|%|vh|vw)$/)) {
      return 'spacing';
    }

    if (value.includes('font') || value.includes('serif') || value.includes('sans')) {
      return 'typography';
    }

    return 'color'; // Default fallback
  }

  // Utility methods
  isUsingDefaultTokens(): boolean {
    if (this.designTokens$.value.length === 0) return true;

    const defaultTokenIds = ['primary-color', 'secondary-color', 'font-family'];
    const currentTokenIds = this.designTokens$.value.map(token => token.id);

    return defaultTokenIds.every(id => currentTokenIds.includes(id)) &&
           this.designTokens$.value.length === defaultTokenIds.length;
  }

  resetToDefaultTokens(): void {
    this.initializeDefaultTokens();
    this.exitDesignTokenEditMode();
  }

  getTokensByType(type: DesignToken['type']): DesignToken[] {
    return this.designTokens$.value.filter(token => token.type === type);
  }

  getTokensByCategory(category: string): DesignToken[] {
    return this.designTokens$.value.filter(token => token.category === category);
  }

  // Color name generation
  generateColorName(hexColor: string): string {
    const hex = hexColor.startsWith('#') ? hexColor.substring(1) : hexColor;

    const colorMap: { [key: string]: string } = {
      '000000': 'Black',
      FFFFFF: 'White',
      FF0000: 'Red',
      '00FF00': 'Green',
      '0000FF': 'Blue',
      FFFF00: 'Yellow',
      '00FFFF': 'Cyan',
      FF00FF: 'Magenta',
      C0C0C0: 'Silver',
      '808080': 'Gray',
      '800000': 'Maroon',
      '808000': 'Olive',
      '008000': 'Dark Green',
      '800080': 'Purple',
      '008080': 'Teal',
      '000080': 'Navy',
      FFA500: 'Orange',
      A52A2A: 'Brown',
      FFC0CB: 'Pink',
      E48900: 'D_Yellow',
      FFA826: 'Lemon',
      '212121': 'Black',
      '4D4D4D': 'D_Grey',
      F5F7FA: 'Silver',
    };

    if (colorMap[hex.toUpperCase()]) {
      return colorMap[hex.toUpperCase()];
    }

    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    let hue = '';
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);

    if (max === min) {
      const brightness = Math.round((r + g + b) / 3);
      if (brightness < 64) return 'Dark Gray';
      if (brightness < 128) return 'Gray';
      if (brightness < 192) return 'Light Gray';
      return 'Off White';
    }

    if (r === max) {
      if (g > b) hue = 'Orange';
      else hue = 'Red';
    } else if (g === max) {
      if (r > b) hue = 'Yellow Green';
      else hue = 'Green';
    } else {
      if (r > g) hue = 'Purple';
      else hue = 'Blue';
    }

    const brightness = (r + g + b) / 3;
    let prefix = '';

    if (brightness < 85) prefix = 'Dark ';
    else if (brightness > 170) prefix = 'Light ';

    return prefix + hue;
  }

  // Backend integration
  getDesignTokensForBackend(projectId: string, jobId: string): any {
    const tokens = this.designTokens$.value;

    const tokensByCategory = {
      colors: tokens
        .filter(token => token.type === 'color')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          hexCode: token.value,
          category: token.category,
          editable: token.editable,
        })),
      typography: tokens
        .filter(token => token.type === 'typography')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          category: token.category,
          editable: token.editable,
        })),
      buttons: tokens
        .filter(token => token.category === 'Buttons')
        .map(token => ({
          id: token.id,
          name: token.name,
          variant: token.value,
          category: token.category,
          editable: token.editable,
        })),
    };

    return {
      designSystem: {
        tokens: tokensByCategory,
        timestamp: new Date().toISOString(),
        projectId: projectId,
        jobId: jobId,
      },
    };
  }

  public sendTokensUpdateRequest(
    tokensPayload: any,
    _projectId: string | null,
    _jobId: string | null,
    _codeGenerationService: any,
    _toastService: any
  ): void {
    // This method would handle sending tokens to backend
    // For now, just log the payload
  }

  triggerDesignTokensUpdate(
    timer: any,
    projectId: string | null,
    jobId: string | null,
    getTokensCallback: () => any,
    sendRequestCallback: (payload: any) => void
  ): void {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }

    if (projectId && jobId) {
      const tokensPayload = getTokensCallback();
      sendRequestCallback(tokensPayload);
    }
  }

  getCurrentDesignTokensState(
    hasDesignSystem: boolean,
    projectId: string | null,
    jobId: string | null,
    getTokensCallback: () => any
  ): any {
    return {
      tokens: this.designTokens$.value,
      hasDesignSystem,
      projectId,
      jobId,
      backendPayload: getTokensCallback(),
    };
  }

  onTokenValueChange(
    event: Event,
    tokenId: string,
    updateTokenCallback: (id: string, value: string) => void,
    isValidColorCallback: (color: string) => boolean
  ): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.value) {
      const value = inputElement.value;
      const tokens = this.designTokens$.value;
      const token = tokens.find(t => t.id === tokenId);

      if (token && token.type === 'color') {
        const isValid = isValidColorCallback(value);

        if (isValid) {
          inputElement.classList.remove('invalid');
          const formattedValue = value.startsWith('#') ? value : `#${value}`;
          updateTokenCallback(tokenId, formattedValue);
        } else {
          inputElement.classList.add('invalid');
          return;
        }
      } else {
        updateTokenCallback(tokenId, value);
      }
    }
  }
}


