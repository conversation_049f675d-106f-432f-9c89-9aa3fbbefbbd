import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CodeWindowUtilsService {

  constructor() { }

  // URL validation and processing
  cleanUrl(url: string): string {
    if (!url || typeof url !== 'string') {
      return '';
    }

    try {
      // Remove any leading/trailing whitespace
      let cleanedUrl = url.trim();

      // Remove any query parameters that might interfere
      const urlObj = new URL(cleanedUrl);
      
      // Remove specific problematic query parameters
      urlObj.searchParams.delete('_');
      urlObj.searchParams.delete('timestamp');
      
      return urlObj.toString();
    } catch (error) {
      // If URL parsing fails, return the original trimmed string
      return url.trim();
    }
  }

  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  isValidPreviewUrl(url: string): { isValid: boolean; errorMessage?: string } {
    try {
      if (!url || typeof url !== 'string' || url.trim() === '') {
        return { isValid: false, errorMessage: 'URL is empty or invalid' };
      }

      const trimmedUrl = url.trim();

      // Check for common invalid patterns
      if (trimmedUrl === 'null' ||
          trimmedUrl === 'undefined' ||
          trimmedUrl === 'ERROR_DEPLOYMENT_FAILED' ||
          trimmedUrl.includes('localhost:0') ||
          trimmedUrl.includes('127.0.0.1:0')) {
        return { isValid: false, errorMessage: 'URL contains invalid patterns' };
      }

      // Validate URL format
      const urlObj = new URL(trimmedUrl);

      // Check for valid protocols
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return {
          isValid: false,
          errorMessage: `Invalid protocol: ${urlObj.protocol}. Only HTTP and HTTPS are allowed.`
        };
      }

      if (!urlObj.hostname || urlObj.hostname.trim() === '') {
        return { isValid: false, errorMessage: 'URL hostname is missing or empty' };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        errorMessage: error instanceof Error ? error.message : 'Invalid URL format'
      };
    }
  }



  // File name processing
  sanitizeFileName(fileName: string): string {
    if (!fileName) return '';

    try {
      // Remove or replace invalid characters
      let sanitized = fileName
        .replace(/[<>:"/\\|?*]/g, '_')  // Replace invalid characters with underscore
        .replace(/\s+/g, '_')          // Replace spaces with underscore
        .replace(/_{2,}/g, '_')        // Replace multiple underscores with single
        .replace(/^_+|_+$/g, '');      // Remove leading/trailing underscores

      // Ensure the filename isn't empty after sanitization
      if (!sanitized) {
        sanitized = 'untitled';
      }

      // Limit length
      if (sanitized.length > 100) {
        sanitized = sanitized.substring(0, 100);
      }

      return sanitized;
    } catch {
      return 'untitled';
    }
  }

  formatFileName(fileName: string): string {
    if (!fileName) return '';

    try {
      // Remove file extension for display
      let formatted = fileName.replace(/\.[^/.]+$/, '');
      
      // Convert camelCase and PascalCase to readable format
      formatted = formatted
        .replace(/([a-z])([A-Z])/g, '$1 $2')
        .replace(/([A-Z])([A-Z][a-z])/g, '$1 $2');
      
      // Convert underscores and hyphens to spaces
      formatted = formatted
        .replace(/[_-]/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
      
      // Capitalize first letter of each word
      formatted = formatted
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');

      return formatted || 'Untitled';
    } catch {
      return 'Untitled';
    }
  }

  extractPageNameFromFileName(fileName: string): string {
    if (!fileName) {
      return 'Untitled Page';
    }

    // Remove file extension
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
    
    // Format the name for display
    return this.formatFileName(nameWithoutExt) + ' Page';
  }

  // File size formatting
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // JSON processing
  parseJsonSafely(jsonString: string): any {
    try {
      return JSON.parse(jsonString);
    } catch {
      return null;
    }
  }

  // ID generation
  generateUniqueId(): string {
    return `id-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  generateNodeId(): string {
    return `ui-design-node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  generateRegenerationSessionId(): string {
    const counter = Date.now();
    return `regen-session-${Date.now()}-${counter}-${Math.random().toString(36).substring(2, 11)}`;
  }

  // Timestamp formatting
  formatTimestamp(timestamp?: number): string {
    const date = timestamp ? new Date(timestamp) : new Date();
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  extractTimestamp(log: string): string {
    const parts = log.split(' - ');
    if (parts.length >= 1) {
      const timestampPart = parts[0].trim();
      
      // Check if it looks like a timestamp (contains colons and numbers)
      if (/\d{2}:\d{2}:\d{2}/.test(timestampPart)) {
        return timestampPart;
      }
      
      // If no clear timestamp, try to extract from beginning
      const timeMatch = timestampPart.match(/(\d{1,2}:\d{2}:\d{2})/);
      if (timeMatch) {
        return timeMatch[1];
      }
    }
    
    // Fallback to current time
    return this.formatTimestamp();
  }

  extractLogContent(log: string): string {
    const parts = log.split(' - ');
    if (parts.length >= 3) {
      return parts.slice(2).join(' - ').trim();
    }
    return log.trim();
  }

  // Object utilities
  deepCloneObject<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.deepCloneObject(item)) as unknown as T;
    }
    
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepCloneObject(obj[key]);
      }
    }
    
    return cloned;
  }

  compareObjects(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) return true;
    
    if (obj1 == null || obj2 == null) return obj1 === obj2;
    
    if (typeof obj1 !== typeof obj2) return false;
    
    if (typeof obj1 !== 'object') return obj1 === obj2;
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!this.compareObjects(obj1[key], obj2[key])) return false;
    }
    
    return true;
  }

  // Function utilities
  debounceFunction<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: number;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => func.apply(this, args), delay);
    };
  }

  throttleFunction<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // Language detection
  getLanguageFromPath(path: string): string {
    const extension = path.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'js':
      case 'jsx':
        return 'javascript';
      case 'ts':
      case 'tsx':
        return 'typescript';
      case 'html':
        return 'html';
      case 'css':
        return 'css';
      case 'scss':
      case 'sass':
        return 'scss';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'py':
        return 'python';
      case 'java':
        return 'java';
      case 'cpp':
      case 'cc':
      case 'cxx':
        return 'cpp';
      case 'c':
        return 'c';
      case 'cs':
        return 'csharp';
      case 'php':
        return 'php';
      case 'rb':
        return 'ruby';
      case 'go':
        return 'go';
      case 'rs':
        return 'rust';
      case 'swift':
        return 'swift';
      case 'kt':
        return 'kotlin';
      default:
        return 'plaintext';
    }
  }

  // Hash generation
  createLogHash(log: string): string {
    const timestamp = this.extractTimestamp(log);
    const content = this.extractLogContent(log);
    return `${timestamp}-${content.substring(0, 50)}`.replace(/[^a-zA-Z0-9]/g, '');
  }



  // Color validation
  isValidHexColor(color: string): boolean {
    const hexColorRegex = /^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexColorRegex.test(color);
  }

  // File tree utilities
  flattenFileTree(files: any[]): any[] {
    const flatFiles: any[] = [];

    const processFile = (file: any, parentPath: string = '') => {
      if (file.type === 'file') {
        const fullPath = file.fileName || file.name || '';
        const finalPath =
          parentPath && !fullPath.startsWith(parentPath) ? `${parentPath}/${file.name}` : fullPath;

        flatFiles.push({
          ...file,
          name: finalPath,
          fileName: finalPath,
        });
      } else if (file.type === 'folder' && file.children) {
        const folderPath = parentPath ? `${parentPath}/${file.name}` : file.name;
        file.children.forEach((child: any) => processFile(child, folderPath));
      }
    };

    files.forEach(file => processFile(file));
    return flatFiles;
  }

  // URL utilities
  addCacheBustingToUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      urlObj.searchParams.set('_refresh', Date.now().toString());
      return urlObj.toString();
    } catch (error) {
      return url;
    }
  }

  // Code conversion utilities
  convertGeneratedCodeToFileModels(generatedCode: any): any[] {
    const fileModels: any[] = [];

    try {
      if (typeof generatedCode === 'string') {
        try {
          const parsedCode = JSON.parse(generatedCode);
          return this.convertGeneratedCodeToFileModels(parsedCode);
        } catch {
          fileModels.push({
            name: 'index.html',
            type: 'file',
            content: generatedCode,
            fileName: 'index.html',
          });
        }
      } else if (Array.isArray(generatedCode)) {
        for (const item of generatedCode) {
          if (typeof item === 'object' && item !== null) {
            const fileName = item.fileName || item.name || item.path || 'unknown.txt';
            const content = item.content || '';
            fileModels.push({
              name: fileName,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              fileName: fileName,
            });
          }
        }
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {
        for (const [filePath, content] of Object.entries(generatedCode)) {
          fileModels.push({
            name: filePath,
            type: 'file',
            content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            fileName: filePath,
          });
        }
      }
    } catch (error) {
      // Silent error handling
    }

    return fileModels;
  }

  // File handling utilities
  handleFileUpload(): void {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';

    fileInput.addEventListener('change', (event: Event) => {
      const input = event.target as HTMLInputElement;
      if (input.files?.length) {
        alert('File uploaded: ' + input.files[0].name);
      }
    });

    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  }

  // Clipboard utilities
  async copyToClipboard(inputElement: HTMLInputElement, toastService: any): Promise<void> {
    try {
      inputElement.select();
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(inputElement.value);
      } else {
        // Fallback for older browsers (deprecated but necessary for compatibility)
        document.execCommand('copy');
      }
      inputElement.setSelectionRange(0, 0);
      toastService.success('Link copied to clipboard');
    } catch (error) {
      toastService.error('Failed to copy link to clipboard');
    }
  }

  // VSCode export utilities
  prepareFilesForVSCodeExport(generatedCode: any): any[] {
    const exportFiles: any[] = [];

    try {
      if (typeof generatedCode === 'string') {
        try {
          const parsedCode = JSON.parse(generatedCode);
          this.processCodeForVSCodeExport(parsedCode, exportFiles);
        } catch {
          exportFiles.push({
            name: 'index.html',
            content: generatedCode,
            path: 'index.html',
          });
        }
      } else if (Array.isArray(generatedCode)) {
        for (const item of generatedCode) {
          if (typeof item === 'object' && item !== null) {
            const fileName = item.fileName || item.name || item.path || 'unknown.txt';
            const content = item.content || '';
            exportFiles.push({
              name: fileName,
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              path: fileName,
            });
          }
        }
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {
        this.processCodeForVSCodeExport(generatedCode, exportFiles);
      }

      return exportFiles;
    } catch (error) {
      return [];
    }
  }

  private processCodeForVSCodeExport(codeObject: any, exportFiles: any[]): void {
    for (const [filePath, content] of Object.entries(codeObject)) {
      exportFiles.push({
        name: filePath,
        content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
        path: filePath,
      });
    }
  }

  handleVSCodeExportResult(result: any, appName: string, toastService: any): void {
    switch (result.method) {
      case 'vscode-protocol':
        if (result.success) {
          toastService.success('🎉 VSCode opened with your project!');
          toastService.info('Your project files have been created and VSCode should be opening now.');
        } else {
          toastService.warning('VSCode protocol attempted but may not have worked.');
          toastService.info("If VSCode didn't open, please check your VSCode installation.");
        }
        break;

      case 'download-fallback':
        const downloadFileName = result.downloadFileName || `${appName.toLowerCase()}.zip`;
        toastService.success(`📦 Project downloaded as "${downloadFileName}"`);
        toastService.info(`💡 Extract ${downloadFileName} → Open VSCode → File → Open Folder → Select extracted folder`);
        toastService.info('🔧 For best experience: Open the .code-workspace file included in the download');
        break;

      case 'manual-instructions':
        toastService.warning('⚠️ Please copy the files manually to your VSCode project');
        break;

      default:
        toastService.error('❌ Unknown export method. Please try downloading the project instead.');
        break;
    }

    if (result.success && result.method === 'vscode-protocol') {
      setTimeout(() => {
        toastService.info('💡 Tip: Look for the new project folder and open the .code-workspace file in VSCode for the best experience!');
      }, 3000);
    }
  }

  // Zip file utilities
  async addFilesToZip(projectFolder: any, generatedCode: any): Promise<void> {
    if (typeof generatedCode === 'string') {
      try {
        const parsedCode = JSON.parse(generatedCode);
        await this.processCodeObject(projectFolder, parsedCode);
      } catch {
        projectFolder.file('index.html', generatedCode);
      }
    } else if (Array.isArray(generatedCode)) {
      for (const item of generatedCode) {
        if (typeof item === 'object' && item !== null) {
          const fileName = item.fileName || item.name || item.path || 'unknown.txt';
          const content = item.content || '';
          this.addFileToZipWithPath(projectFolder, fileName, content);
        }
      }
    } else if (typeof generatedCode === 'object' && generatedCode !== null) {
      await this.processCodeObject(projectFolder, generatedCode);
    }
  }

  private async processCodeObject(projectFolder: any, codeObject: any): Promise<void> {
    for (const [filePath, content] of Object.entries(codeObject)) {
      const fileContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
      this.addFileToZipWithPath(projectFolder, filePath, fileContent);
    }
  }

  private addFileToZipWithPath(projectFolder: any, filePath: string, content: string): void {
    const cleanPath = filePath.replace(/^\/+/, '');
    const pathParts = cleanPath.split('/');
    const fileName = pathParts.pop() || 'unknown.txt';

    let currentFolder = projectFolder;
    for (const folderName of pathParts) {
      if (folderName.trim()) {
        const existingFolder = currentFolder.folder(folderName);
        currentFolder = existingFolder || currentFolder.folder(folderName)!;
      }
    }

    currentFolder.file(fileName, content);
  }

  addProjectMetadata(projectFolder: any, appName: string): void {
    const readmeContent = this.generateReadmeContent(appName);
    projectFolder.file('README.md', readmeContent);
  }

  private generateReadmeContent(appName: string): string {
    const currentDate = new Date().toLocaleDateString();
    return `# ${appName}

Generated on: ${currentDate}

## Description
This project was generated using the Experience Studio platform.

## Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn

### Installation
\`\`\`bash
npm install
\`\`\`

### Running the Application
\`\`\`bash
npm start
\`\`\`

### Building for Production
\`\`\`bash
npm run build
\`\`\`

## Project Structure
- \`src/\` - Source code files
- \`public/\` - Static assets

## Support
For support and questions, please refer to the Experience Studio documentation.
`;
  }

  triggerDownload(zipBlob: Blob, appName: string): void {
    try {
      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${appName}.zip`;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      throw new Error('Failed to trigger download');
    }
  }

  generateAppNameForDownload(appName?: string, projectName?: string, promptData?: any): string {
    if (appName) {
      return appName;
    }

    if (projectName) {
      return projectName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    if (promptData?.selectedCardTitle) {
      const baseName = promptData.selectedCardTitle.toLowerCase().replace(/[^a-z0-9]/g, '-');
      return `${baseName}-app`;
    }

    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `generated-app-${timestamp}`;
  }

  getCodeFromMultipleSources(codeSharingService: any, files: any, artifactsData: any): any {
    let generatedCode = codeSharingService.getGeneratedCode();
    if (generatedCode) {
      return generatedCode;
    }

    let currentFiles: any = null;
    files.subscribe((filesData: any) => (currentFiles = filesData)).unsubscribe();
    if (currentFiles && currentFiles.length > 0) {
      return currentFiles;
    }

    if (artifactsData && artifactsData.length > 0) {
      const codeArtifacts = artifactsData.filter(
        (artifact: any) => artifact.type === 'file' || artifact.type === 'code'
      );
      if (codeArtifacts.length > 0) {
        return codeArtifacts;
      }
    }

    return null;
  }
}
