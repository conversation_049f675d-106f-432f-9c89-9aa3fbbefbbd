import { Injectable, inject, DestroyRef } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
// import { Router } from '@angular/router'; // Reserved for future routing operations

export interface TabState {
  activeTab: string;
  isTransitioning: boolean;
  lastError?: string;
}

export interface LoadingStates {
  isUIDesignLoading: boolean;
  isCodeGenerationLoading: boolean;
  isPreviewLoading: boolean;
  isArtifactsLoading: boolean;
  isRegenerationInProgress: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowStateService {
  private destroyRef = inject(DestroyRef);
  // private router = inject(Router); // Reserved for future routing operations
  private route = inject(ActivatedRoute);

  // State management BehaviorSubjects
  private currentView$ = new BehaviorSubject<'loading' | 'editor' | 'preview' | 'overview' | 'logs' | 'artifacts'>('preview');
  private isLoading$ = new BehaviorSubject<boolean>(true);
  private isHistoryActive$ = new BehaviorSubject<boolean>(false);
  private isCodeActive$ = new BehaviorSubject<boolean>(false);
  private isPreviewActive$ = new BehaviorSubject<boolean>(true);
  private isArtifactsActive$ = new BehaviorSubject<boolean>(false);
  private isLeftPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private shouldHideProjectName$ = new BehaviorSubject<boolean>(false);

  // Tab state management
  private currentTabState$ = new BehaviorSubject<TabState>({
    activeTab: 'preview',
    isTransitioning: false
  });

  // Loading states
  private loadingStates$ = new BehaviorSubject<LoadingStates>({
    isUIDesignLoading: false,
    isCodeGenerationLoading: false,
    isPreviewLoading: false,
    isArtifactsLoading: false,
    isRegenerationInProgress: false
  });

  // UI Design mode state
  private isUIDesignMode$ = new BehaviorSubject<boolean>(false);
  private isUIDesignGenerating$ = new BehaviorSubject<boolean>(false);
  private isUIDesignRegenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignApiInProgress$ = new BehaviorSubject<boolean>(false);

  // Component state
  private componentState$ = new BehaviorSubject<any>({
    isCodeGenerationComplete: false,
    isPromptBarEnabled: false,
    userSelectedTab: false,
    pollingStatus: 'PENDING',
    currentProgressState: '',
    lastProgressDescription: ''
  });

  constructor() {
    this.initializeStateManagement();
  }

  // Public getters for observables
  get currentView(): Observable<string> { return this.currentView$.asObservable(); }
  get isLoading(): Observable<boolean> { return this.isLoading$.asObservable(); }
  get isHistoryActive(): Observable<boolean> { return this.isHistoryActive$.asObservable(); }
  get isCodeActive(): Observable<boolean> { return this.isCodeActive$.asObservable(); }
  get isPreviewActive(): Observable<boolean> { return this.isPreviewActive$.asObservable(); }
  get isArtifactsActive(): Observable<boolean> { return this.isArtifactsActive$.asObservable(); }
  get isLeftPanelCollapsed(): Observable<boolean> { return this.isLeftPanelCollapsed$.asObservable(); }
  get shouldHideProjectName(): Observable<boolean> { return this.shouldHideProjectName$.asObservable(); }
  get currentTabState(): Observable<TabState> { return this.currentTabState$.asObservable(); }
  get loadingStates(): Observable<LoadingStates> { return this.loadingStates$.asObservable(); }
  get isUIDesignMode(): Observable<boolean> { return this.isUIDesignMode$.asObservable(); }
  get isUIDesignGenerating(): Observable<boolean> { return this.isUIDesignGenerating$.asObservable(); }
  get isUIDesignRegenerating(): Observable<boolean> { return this.isUIDesignRegenerating$.asObservable(); }
  get uiDesignApiInProgress(): Observable<boolean> { return this.uiDesignApiInProgress$.asObservable(); }
  get componentState(): Observable<any> { return this.componentState$.asObservable(); }

  // Public getters for current values
  get currentViewValue(): string { return this.currentView$.value; }
  get isLoadingValue(): boolean { return this.isLoading$.value; }
  get isHistoryActiveValue(): boolean { return this.isHistoryActive$.value; }
  get isCodeActiveValue(): boolean { return this.isCodeActive$.value; }
  get isPreviewActiveValue(): boolean { return this.isPreviewActive$.value; }
  get isArtifactsActiveValue(): boolean { return this.isArtifactsActive$.value; }
  get isLeftPanelCollapsedValue(): boolean { return this.isLeftPanelCollapsed$.value; }
  get shouldHideProjectNameValue(): boolean { return this.shouldHideProjectName$.value; }
  get currentTabStateValue(): TabState { return this.currentTabState$.value; }
  get loadingStatesValue(): LoadingStates { return this.loadingStates$.value; }
  get isUIDesignModeValue(): boolean { return this.isUIDesignMode$.value; }
  get isUIDesignGeneratingValue(): boolean { return this.isUIDesignGenerating$.value; }
  get isUIDesignRegeneratingValue(): boolean { return this.isUIDesignRegenerating$.value; }
  get uiDesignApiInProgressValue(): boolean { return this.uiDesignApiInProgress$.value; }
  get componentStateValue(): any { return this.componentState$.value; }

  // State update methods
  updateCurrentView(view: 'loading' | 'editor' | 'preview' | 'overview' | 'logs' | 'artifacts'): void {
    this.currentView$.next(view);
  }

  updateLoadingState(isLoading: boolean): void {
    this.isLoading$.next(isLoading);
  }

  updateHistoryActiveState(isActive: boolean): void {
    this.isHistoryActive$.next(isActive);
  }

  updateCodeActiveState(isActive: boolean): void {
    this.isCodeActive$.next(isActive);
  }

  updatePreviewActiveState(isActive: boolean): void {
    this.isPreviewActive$.next(isActive);
  }

  updateArtifactsActiveState(isActive: boolean): void {
    this.isArtifactsActive$.next(isActive);
  }

  updateLeftPanelCollapsedState(isCollapsed: boolean): void {
    this.isLeftPanelCollapsed$.next(isCollapsed);
  }

  updateShouldHideProjectName(shouldHide: boolean): void {
    this.shouldHideProjectName$.next(shouldHide);
  }

  updateTabState(tabState: Partial<TabState>): void {
    const currentState = this.currentTabState$.value;
    this.currentTabState$.next({ ...currentState, ...tabState });
  }

  updateLoadingStates(loadingStates: Partial<LoadingStates>): void {
    const currentStates = this.loadingStates$.value;
    this.loadingStates$.next({ ...currentStates, ...loadingStates });
  }

  updateUIDesignMode(isUIDesignMode: boolean): void {
    this.isUIDesignMode$.next(isUIDesignMode);
  }

  updateUIDesignGenerating(isGenerating: boolean): void {
    this.isUIDesignGenerating$.next(isGenerating);
  }

  updateUIDesignRegenerating(isRegenerating: boolean): void {
    this.isUIDesignRegenerating$.next(isRegenerating);
  }

  updateUIDesignApiInProgress(inProgress: boolean): void {
    this.uiDesignApiInProgress$.next(inProgress);
  }

  updateComponentState(state: Partial<any>): void {
    const currentState = this.componentState$.value;
    this.componentState$.next({ ...currentState, ...state });
  }

  // Tab switching methods
  switchToCodeTab(): void {
    this.updateCurrentView('editor');
    this.updateCodeActiveState(true);
    this.updatePreviewActiveState(false);
    this.updateArtifactsActiveState(false);
  }

  switchToPreviewTab(): void {
    this.updateCurrentView('preview');
    this.updateCodeActiveState(false);
    this.updatePreviewActiveState(true);
    this.updateArtifactsActiveState(false);
  }

  switchToArtifactsTab(): void {
    this.updateCurrentView('artifacts');
    this.updateCodeActiveState(false);
    this.updatePreviewActiveState(false);
    this.updateArtifactsActiveState(true);
  }

  switchToLogsTab(): void {
    this.updateCurrentView('logs');
    this.updateCodeActiveState(false);
    this.updatePreviewActiveState(false);
    this.updateArtifactsActiveState(false);
  }

  switchToOverviewTab(): void {
    this.updateCurrentView('overview');
    this.updateCodeActiveState(false);
    this.updatePreviewActiveState(false);
    this.updateArtifactsActiveState(false);
  }

  // Tab transition management
  startTabTransition(targetTab: string): void {
    this.updateTabState({
      activeTab: targetTab,
      isTransitioning: true
    });
  }

  completeTabTransition(): void {
    setTimeout(() => {
      this.updateTabState({
        isTransitioning: false
      });
    }, 200);
  }

  // State reset methods
  resetComponentState(): void {
    // Reset component state using proper update methods
    this.updateComponentState({
      isCodeGenerationComplete: false,
      isPromptBarEnabled: false,
      userSelectedTab: false,
      pollingStatus: 'PENDING',
      currentProgressState: '',
      lastProgressDescription: ''
    });

    // Reset tab states
    this.updateCurrentView('preview');
    this.updatePreviewActiveState(true);
    this.updateCodeActiveState(false);
    this.updateArtifactsActiveState(false);

    // Reset loading states
    this.resetLoadingStates();

    // Reset UI Design state
    this.resetUIDesignState();

    // Reset tab state
    this.resetTabState();
  }

  resetUIDesignState(): void {
    this.updateUIDesignMode(false);
    this.updateUIDesignGenerating(false);
    this.updateUIDesignRegenerating(false);
    this.updateUIDesignApiInProgress(false);
  }

  resetTabState(): void {
    this.updateTabState({
      activeTab: 'preview',
      isTransitioning: false,
      lastError: undefined
    });
  }

  resetLoadingStates(): void {
    this.updateLoadingStates({
      isUIDesignLoading: false,
      isCodeGenerationLoading: false,
      isPreviewLoading: false,
      isArtifactsLoading: false,
      isRegenerationInProgress: false
    });
  }

  // Tab state management methods
  setPreviewTabDisabled(_message: string = 'Preview will be available once the application is deployed'): void {
    // This would need to be implemented with proper tab enabled state management
    // For now, we'll use the tab state to track this
    this.updateTabState({
      activeTab: 'preview',
      isTransitioning: false
    });
  }

  setCodeTabDisabled(_message: string = 'Code will be available once generated'): void {
    // This would need to be implemented with proper tab enabled state management
    // For now, we'll use the tab state to track this
    this.updateTabState({
      activeTab: 'code',
      isTransitioning: false
    });
  }

  setArtifactsTabDisabled(_message: string = 'Artifacts will be available once generated'): void {
    // This would need to be implemented with proper tab enabled state management
    // For now, we'll use the tab state to track this
    this.updateTabState({
      activeTab: 'artifacts',
      isTransitioning: false
    });
  }

  // Regeneration state management
  validateAndResetRegenerationState(): void {
    this.updateLoadingStates({
      isCodeGenerationLoading: false,
      isRegenerationInProgress: false
    });
  }

  resetRegenerationCallFlag(): void {
    // This would reset regeneration call flags in the component state
    // For now, we'll update the loading states
    this.updateLoadingStates({
      isRegenerationInProgress: false
    });
  }

  performRegenerationErrorCleanup(): void {
    // Reset state-related cleanup for regeneration errors
    this.updateLoadingStates({
      isRegenerationInProgress: false,
      isCodeGenerationLoading: false,
      isPreviewLoading: false
    });

    // Reset UI state
    this.resetUIDesignState();
  }

  startRegenerationPreviewLoading(): void {
    this.updateLoadingStates({
      isRegenerationInProgress: true,
      isPreviewLoading: true
    });
  }

  stopRegenerationPreviewLoading(): void {
    this.updateLoadingStates({
      isPreviewLoading: false
    });
  }

  handleRegenerationFailure(): void {
    this.updateLoadingStates({
      isRegenerationInProgress: false,
      isPreviewLoading: false
    });
  }

  completeDirectRegenerationResponse(): void {
    this.updateLoadingStates({
      isRegenerationInProgress: false,
      isCodeGenerationLoading: false,
      isPreviewLoading: false
    });
  }

  ensureIframeLoadedAfterRegeneration(): void {
    // Reset URL validation states
    this.updateLoadingStates({
      isPreviewLoading: false
    });
  }

  restoreIframeAfterRegenerationWithoutUrl(): void {
    this.updateLoadingStates({
      isPreviewLoading: false
    });
  }

  restoreIframeAfterRegenerationError(): void {
    this.updateLoadingStates({
      isPreviewLoading: false
    });
  }

  // Local storage methods
  saveStateToLocalStorage(): void {
    try {
      const stateToSave = {
        currentView: this.currentViewValue,
        isLeftPanelCollapsed: this.isLeftPanelCollapsedValue,
        currentTabState: this.currentTabStateValue,
        componentState: this.componentStateValue
      };
      localStorage.setItem('codeWindowState', JSON.stringify(stateToSave));
    } catch (error) {
    }
  }

  loadStateFromLocalStorage(): void {
    try {
      const savedState = localStorage.getItem('codeWindowState');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        if (parsedState.currentView) {
          this.updateCurrentView(parsedState.currentView);
        }
        if (typeof parsedState.isLeftPanelCollapsed === 'boolean') {
          this.updateLeftPanelCollapsedState(parsedState.isLeftPanelCollapsed);
        }
        if (parsedState.currentTabState) {
          this.updateTabState(parsedState.currentTabState);
        }
        if (parsedState.componentState) {
          this.updateComponentState(parsedState.componentState);
        }
      }
    } catch (error) {
    }
  }

  // State validation
  validateStateConsistency(): boolean {
    const activeStates = [
      this.isCodeActiveValue,
      this.isPreviewActiveValue,
      this.isArtifactsActiveValue
    ];

    const activeCount = activeStates.filter(state => state).length;
    return activeCount <= 1; // Only one tab should be active at a time
  }

  // UI Design mode initialization
  private initializeUIDesignMode(): void {
    this.route.data.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(data => {
      const isUIDesignMode = data['cardType'] === 'Generate UI Design';
      const wasUIDesignMode = this.isUIDesignModeValue;

      if (wasUIDesignMode && !isUIDesignMode) {
        this.cleanupUIDesignMode();
      }

      this.updateUIDesignMode(isUIDesignMode);

      if (isUIDesignMode) {
        this.setupUIDesignMode();
      }
    });
  }

  private setupUIDesignMode(): void {
    this.updateCurrentView('preview');
    this.updatePreviewActiveState(true);
    this.updateCodeActiveState(false);
    this.updateArtifactsActiveState(false);

    this.updateComponentState({
      isPolling: false,
      pollingStatus: 'PENDING',
      currentProgressState: '',
      lastProgressDescription: '',
      isCodeGenerationComplete: false
    });

    this.updateUIDesignGenerating(false);
    this.updateUIDesignRegenerating(false);
    this.updateUIDesignApiInProgress(false);
  }

  private cleanupUIDesignMode(): void {
    this.resetUIDesignState();
  }

  private initializeStateManagement(): void {
    this.initializeUIDesignMode();
    this.loadStateFromLocalStorage();
  }
}
