// Intro Message Shimmer Component Styles - Optimized for intro message loading
.intro-message-shimmer-container {
  width: 100%;
  min-height: 24px;
  display: flex;
  background: transparent; // Completely transparent container
}

.intro-shimmer-line {
  width: 50%; // Set width to 50% as requested
  height: 24px;
  border-radius: 4px;
  // Enhanced light theme shimmer - more visible
  background: linear-gradient(to right, #d8d8d8 8%, #c8c8c8 18%, #d8d8d8 33%);

  background-size: 800px 104px;
  animation: introShimmer 1s infinite linear;
}

// Light theme (default) - more visible shimmer
.light-theme .intro-shimmer-line {
  background: linear-gradient(to right, #d8d8d8 8%, #c8c8c8 18%, #d8d8d8 33%);

  background-size: 800px 104px;
  animation: introShimmer 1s infinite linear;
}

// Dark theme - keeping original visibility
.dark-theme .intro-shimmer-line {
  background: linear-gradient(to right, #3a3a3a 8%, #444 18%, #3a3a3a 33%);

  background-size: 800px 104px;
  animation: introShimmer 1s infinite linear;
}

// Intro shimmer animation - optimized for intro message loading
@keyframes introShimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}
