import { Injectable, OnDestroy } from '@angular/core';
import { Router, NavigationStart, NavigationEnd } from '@angular/router';
import { Location } from '@angular/common';
import { BehaviorSubject, Subject, fromEvent } from 'rxjs';
import { filter, takeUntil, debounceTime } from 'rxjs/operators';
import { createLogger } from '../utils';

export interface NavigationCleanupConfig {
  clearSessionStorage: boolean;
  clearLocalStorage: boolean;
  clearArtifacts: boolean;
  clearLogs: boolean;
  clearPreviewData: boolean;
  clearUIDesignData: boolean;
}

export interface CleanupEvent {
  type: 'browser-navigation' | 'route-change' | 'manual' | 'beforeunload';
  fromRoute?: string;
  toRoute?: string;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class NavigationCleanupService implements OnDestroy {
  private readonly logger = createLogger('NavigationCleanupService');
  private readonly destroy$ = new Subject<void>();
  
  // State management
  private isCleanupInProgress$ = new BehaviorSubject<boolean>(false);
  private lastCleanupEvent$ = new BehaviorSubject<CleanupEvent | null>(null);
  
  // Configuration
  private defaultConfig: NavigationCleanupConfig = {
    clearSessionStorage: true,
    clearLocalStorage: false,
    clearArtifacts: true,
    clearLogs: true,
    clearPreviewData: true,
    clearUIDesignData: true
  };

  // Routes that should trigger cleanup when navigating away
  private readonly codePreviewRoutes = [
    '/experience/code-preview',
    '/experience/generate-application/code-preview',
    '/experience/generate-ui-design/code-preview'
  ];

  // Session storage keys to clear
  private readonly sessionStorageKeys = [
    'codeWindowState',
    'artifactsData',
    'designSystemData',
    'layoutAnalyzedData',
    'projectInfo',
    'previewUrl',
    'deployedUrl',
    'codeFiles',
    'fileTree',
    'monacoState',
    'editorState',
    'uiDesignNodes',
    'canvasState',
    'file_tree_baseline',
    'file_tree_current',
    'file_tree_version',
    'file_version_histories',
    'user_edited_files',
    'monaco_editor_states',
    'monaco_model_states',
    'monaco_dirty_files',
    'completeSelections',
    'appState'
  ];

  // Local storage keys to clear (if enabled)
  private readonly localStorageKeys = [
    'codeWindowPreferences',
    'artifactsPreferences',
    'designTokens',
    'layoutPreferences'
  ];

  constructor(
    private router: Router,
    private location: Location
  ) {
    this.initializeNavigationListeners();
    this.logger.info('🧹 Navigation Cleanup Service initialized');
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.logger.info('🧹 Navigation Cleanup Service destroyed');
  }

  /**
   * Initialize all navigation event listeners
   */
  private initializeNavigationListeners(): void {
    this.setupRouterEventListeners();
    this.setupBrowserNavigationListeners();
    this.setupBeforeUnloadListener();
  }

  /**
   * Setup router event listeners for route changes
   */
  private setupRouterEventListeners(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationStart),
        takeUntil(this.destroy$)
      )
      .subscribe((event: NavigationStart) => {
        this.handleRouteNavigationStart(event);
      });

    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe((event: NavigationEnd) => {
        this.handleRouteNavigationEnd(event);
      });
  }

  /**
   * Setup browser navigation listeners (back/forward buttons)
   */
  private setupBrowserNavigationListeners(): void {
    // Listen for popstate events (browser back/forward)
    fromEvent(window, 'popstate')
      .pipe(
        debounceTime(100), // Debounce to prevent multiple rapid events
        takeUntil(this.destroy$)
      )
      .subscribe((event: Event) => {
        this.handleBrowserNavigation(event as PopStateEvent);
      });

    // Listen for page visibility changes
    fromEvent(document, 'visibilitychange')
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (document.hidden) {
          this.handlePageHidden();
        }
      });

    // Listen for pagehide events
    fromEvent(window, 'pagehide')
      .pipe(takeUntil(this.destroy$))
      .subscribe((event: Event) => {
        this.handlePageHide(event as PageTransitionEvent);
      });
  }

  /**
   * Setup beforeunload listener for page refresh/close
   */
  private setupBeforeUnloadListener(): void {
    fromEvent(window, 'beforeunload')
      .pipe(takeUntil(this.destroy$))
      .subscribe((event: Event) => {
        this.handleBeforeUnload(event as BeforeUnloadEvent);
      });
  }

  /**
   * Handle route navigation start
   */
  private handleRouteNavigationStart(event: NavigationStart): void {
    const currentUrl = this.router.url;
    const targetUrl = event.url;
    
    this.logger.info(`🔄 Route navigation start: ${currentUrl} → ${targetUrl}`);
    
    // Check if navigating away from code-preview routes
    if (this.isCodePreviewRoute(currentUrl) && !this.isCodePreviewRoute(targetUrl)) {
      this.logger.info('🧹 Navigating away from code-preview route, triggering cleanup');
      this.performCleanup({
        type: 'route-change',
        fromRoute: currentUrl,
        toRoute: targetUrl,
        timestamp: new Date()
      });
    }
  }

  /**
   * Handle route navigation end
   */
  private handleRouteNavigationEnd(event: NavigationEnd): void {
    this.logger.info(`✅ Route navigation completed: ${event.url}`);
  }

  /**
   * Handle browser navigation (back/forward buttons)
   */
  private handleBrowserNavigation(_event: PopStateEvent): void {
    const currentUrl = this.location.path();

    this.logger.info(`🔙 Browser navigation detected: ${currentUrl}`);

    // Always trigger cleanup on browser navigation if coming from code-preview
    if (this.wasOnCodePreviewRoute()) {
      this.logger.info('🧹 Browser navigation from code-preview detected, triggering cleanup');
      this.performCleanup({
        type: 'browser-navigation',
        fromRoute: this.getPreviousRoute(),
        toRoute: currentUrl,
        timestamp: new Date()
      });
    }
  }

  /**
   * Handle page hidden (tab switch, minimize)
   */
  private handlePageHidden(): void {
    const currentUrl = this.router.url;
    
    if (this.isCodePreviewRoute(currentUrl)) {
      this.logger.info('👁️ Page hidden while on code-preview route, performing light cleanup');
      // Perform lighter cleanup for page visibility changes
      this.performCleanup({
        type: 'browser-navigation',
        fromRoute: currentUrl,
        timestamp: new Date()
      }, { ...this.defaultConfig, clearLocalStorage: false });
    }
  }

  /**
   * Handle page hide event
   */
  private handlePageHide(_event: PageTransitionEvent): void {
    const currentUrl = this.router.url;

    if (this.isCodePreviewRoute(currentUrl)) {
      this.logger.info('📄 Page hide event on code-preview route, triggering cleanup');
      this.performCleanup({
        type: 'browser-navigation',
        fromRoute: currentUrl,
        timestamp: new Date()
      });
    }
  }

  /**
   * Handle before unload (page refresh/close)
   */
  private handleBeforeUnload(_event: BeforeUnloadEvent): void {
    const currentUrl = this.router.url;

    if (this.isCodePreviewRoute(currentUrl)) {
      this.logger.info('🔄 Before unload on code-preview route, triggering cleanup');
      this.performCleanup({
        type: 'beforeunload',
        fromRoute: currentUrl,
        timestamp: new Date()
      });
    }
  }

  /**
   * Check if a route is a code-preview route
   */
  private isCodePreviewRoute(url: string): boolean {
    return this.codePreviewRoutes.some(route => url.includes('code-preview'));
  }

  /**
   * Check if user was previously on a code-preview route
   */
  private wasOnCodePreviewRoute(): boolean {
    // This would need to be tracked via session storage or service state
    const lastRoute = sessionStorage.getItem('lastRoute');
    return lastRoute ? this.isCodePreviewRoute(lastRoute) : false;
  }

  /**
   * Get the previous route
   */
  private getPreviousRoute(): string {
    return sessionStorage.getItem('lastRoute') || '';
  }

  /**
   * Perform the actual cleanup
   */
  private performCleanup(event: CleanupEvent, config: NavigationCleanupConfig = this.defaultConfig): void {
    if (this.isCleanupInProgress$.value) {
      this.logger.warn('⚠️ Cleanup already in progress, skipping');
      return;
    }

    this.isCleanupInProgress$.next(true);
    this.lastCleanupEvent$.next(event);

    try {
      this.logger.info('🧹 Starting navigation cleanup', { event, config });

      if (config.clearSessionStorage) {
        this.clearSessionStorageData();
      }

      if (config.clearLocalStorage) {
        this.clearLocalStorageData();
      }

      // Store current route for future reference
      sessionStorage.setItem('lastRoute', this.router.url);

      this.logger.info('✅ Navigation cleanup completed successfully');
    } catch (error) {
      this.logger.error('❌ Navigation cleanup failed:', error);
    } finally {
      this.isCleanupInProgress$.next(false);
    }
  }

  /**
   * Clear session storage data
   */
  private clearSessionStorageData(): void {
    this.logger.info('🗑️ Clearing session storage data');
    
    this.sessionStorageKeys.forEach(key => {
      try {
        sessionStorage.removeItem(key);
      } catch (error) {
        this.logger.warn(`⚠️ Failed to remove session storage key: ${key}`, error);
      }
    });
  }

  /**
   * Clear local storage data
   */
  private clearLocalStorageData(): void {
    this.logger.info('🗑️ Clearing local storage data');
    
    this.localStorageKeys.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        this.logger.warn(`⚠️ Failed to remove local storage key: ${key}`, error);
      }
    });
  }

  /**
   * Public API: Manually trigger cleanup
   */
  public triggerManualCleanup(config?: Partial<NavigationCleanupConfig>): void {
    const cleanupConfig = { ...this.defaultConfig, ...config };
    
    this.performCleanup({
      type: 'manual',
      fromRoute: this.router.url,
      timestamp: new Date()
    }, cleanupConfig);
  }

  /**
   * Public API: Get cleanup status
   */
  public getCleanupStatus() {
    return {
      isInProgress: this.isCleanupInProgress$.value,
      lastEvent: this.lastCleanupEvent$.value
    };
  }

  /**
   * Public API: Get observables for status monitoring
   */
  public getStatusObservables() {
    return {
      isCleanupInProgress$: this.isCleanupInProgress$.asObservable(),
      lastCleanupEvent$: this.lastCleanupEvent$.asObservable()
    };
  }
}
