import { Injectable, signal } from '@angular/core';
import { createLogger } from '../utils/logger';

export interface GenerationSelections {
  framework: string;
  designLibrary: string;
  applicationTarget: string;
}

export interface RepositoryMetadata {
  cloneUrl: string;
  repositoryName: string;
  repositoryUrl: string;
  branchName?: string;
  commitHash?: string;
}

export interface GenerationState {
  selections: GenerationSelections;
  repositoryMetadata: RepositoryMetadata | null;
  isTemplateLoading: boolean;
  isCodeTabEnabled: boolean;
}

// Validation constants for allowed values
export const ALLOWED_FRAMEWORKS = ['react', 'angular', 'vue'] as const;
export const ALLOWED_DESIGN_LIBRARIES = ['tailwindcss', 'materialui', 'bootstrap'] as const;

export type AllowedFramework = typeof ALLOWED_FRAMEWORKS[number];
export type AllowedDesignLibrary = typeof ALLOWED_DESIGN_LIBRARIES[number];

/**
 * Service to manage shared generation state across components
 * Uses Angular 19+ Signals for reactive state management
 */
@Injectable({
  providedIn: 'root'
})
export class GenerationStateService {
  private readonly logger = createLogger('GenerationState');

  // Angular 19+ Signals for reactive state management
  public readonly generationState = signal<GenerationState>({
    selections: {
      framework: 'react',
      designLibrary: 'tailwindcss',
      applicationTarget: 'web'
    },
    repositoryMetadata: null,
    isTemplateLoading: false,
    isCodeTabEnabled: false
  });

  constructor() {
    this.logger.info('🏗️ GenerationStateService initialized with Angular 19+ Signals');
  }

  /**
   * Validate framework value against allowed values
   */
  private validateFramework(framework: string): AllowedFramework {
    if (!ALLOWED_FRAMEWORKS.includes(framework as AllowedFramework)) {
      this.logger.warn('⚠️ Invalid framework provided, using default', {
        provided: framework,
        allowed: ALLOWED_FRAMEWORKS,
        defaultUsed: 'react'
      });
      return 'react';
    }
    return framework as AllowedFramework;
  }

  /**
   * Validate design library value against allowed values
   */
  private validateDesignLibrary(designLibrary: string): AllowedDesignLibrary {
    if (!ALLOWED_DESIGN_LIBRARIES.includes(designLibrary as AllowedDesignLibrary)) {
      this.logger.warn('⚠️ Invalid design library provided, using default', {
        provided: designLibrary,
        allowed: ALLOWED_DESIGN_LIBRARIES,
        defaultUsed: 'tailwindcss'
      });
      return 'tailwindcss';
    }
    return designLibrary as AllowedDesignLibrary;
  }

  /**
   * Update framework and design library selections with strict validation
   */
  updateSelections(framework: string, designLibrary: string, applicationTarget: string = 'web'): void {
    // Validate inputs against allowed values
    const validatedFramework = this.validateFramework(framework);
    const validatedDesignLibrary = this.validateDesignLibrary(designLibrary);

    const currentState = this.generationState();
    this.generationState.set({
      ...currentState,
      selections: {
        framework: validatedFramework,
        designLibrary: validatedDesignLibrary,
        applicationTarget
      }
    });

    this.logger.info('📋 Updated generation selections with validation', {
      original: { framework, designLibrary, applicationTarget },
      validated: { framework: validatedFramework, designLibrary: validatedDesignLibrary, applicationTarget }
    });
  }

  /**
   * Update repository metadata from SSE events
   */
  updateRepositoryMetadata(metadata: RepositoryMetadata): void {
    const currentState = this.generationState();
    this.generationState.set({
      ...currentState,
      repositoryMetadata: metadata
    });

    this.logger.info('🔗 Updated repository metadata', {
      cloneUrl: metadata.cloneUrl,
      repositoryName: metadata.repositoryName
    });
  }

  /**
   * Update template loading state
   */
  updateTemplateLoadingState(isLoading: boolean): void {
    const currentState = this.generationState();
    this.generationState.set({
      ...currentState,
      isTemplateLoading: isLoading
    });

    this.logger.info('🏗️ Updated template loading state', { isLoading });
  }

  /**
   * Update code tab enabled state
   */
  updateCodeTabState(isEnabled: boolean): void {
    const currentState = this.generationState();
    this.generationState.set({
      ...currentState,
      isCodeTabEnabled: isEnabled
    });

    this.logger.info('📝 Updated code tab state', { isEnabled });
  }

  /**
   * Get current framework selection
   */
  getCurrentFramework(): string {
    return this.generationState().selections.framework;
  }

  /**
   * Get current design library selection
   */
  getCurrentDesignLibrary(): string {
    return this.generationState().selections.designLibrary;
  }

  /**
   * Get current repository metadata
   */
  getCurrentRepositoryMetadata(): RepositoryMetadata | null {
    return this.generationState().repositoryMetadata;
  }

  /**
   * Check if template is currently loading
   */
  isTemplateLoading(): boolean {
    return this.generationState().isTemplateLoading;
  }

  /**
   * Check if code tab is enabled
   */
  isCodeTabEnabled(): boolean {
    return this.generationState().isCodeTabEnabled;
  }

  /**
   * Reset all state to defaults
   */
  resetState(): void {
    this.generationState.set({
      selections: {
        framework: 'react',
        designLibrary: 'tailwindcss',
        applicationTarget: 'web'
      },
      repositoryMetadata: null,
      isTemplateLoading: false,
      isCodeTabEnabled: false
    });

    this.logger.info('🔄 Generation state reset to defaults');
  }
}
