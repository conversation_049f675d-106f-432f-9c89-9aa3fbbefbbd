/**
 * Quick test to verify SSE URL building with since=0 parameter
 * This can be run in the browser console to test the implementation
 */

// Mock environment for testing
const mockEnvironment = {
  apiUrl: 'https://api.example.com'
};



// Test SSE Options interface
interface TestSSEOptions {
  generationType?: 'initial-code-gen' | 'code-regen' | 'unknown';
  enableSinceParameter?: boolean;
  queryParameters?: Record<string, string>;
}

// Test implementation of shouldAddSinceParameter
function testShouldAddSinceParameter(options?: TestSSEOptions): boolean {
  const sinceEnabled = options?.enableSinceParameter ?? true; // Default to true
  const isInitialGeneration = options?.generationType === 'initial-code-gen';
  const shouldAdd = sinceEnabled && isInitialGeneration;



  return shouldAdd;
}

// Test implementation of buildQueryParameters
function testBuildQueryParameters(options?: TestSSEOptions): string {
  const params: Record<string, string> = {};



  // Add custom query parameters from options
  if (options?.queryParameters) {
    Object.assign(params, options.queryParameters);
  }

  // Add since=0 parameter for initial code generation
  if (testShouldAddSinceParameter(options)) {
    params['since'] = '0';
   
  } else {
  }

  // Convert to query string
  const queryString = Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');

  

  return queryString;
}

// Test implementation of buildSSEUrl
function testBuildSSEUrl(jobId: string, options?: TestSSEOptions): string {
  const baseUrl = mockEnvironment.apiUrl;
  let url = `${baseUrl}/stream/project-status/${jobId}`;

  const queryParams = testBuildQueryParameters(options);
  if (queryParams) {
    url += `?${queryParams}`;
  }
  return url;
}
