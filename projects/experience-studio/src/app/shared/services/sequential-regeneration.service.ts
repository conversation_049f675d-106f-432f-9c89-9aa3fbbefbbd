import { Injectable, inject, DestroyRef, <PERSON><PERSON>one } from '@angular/core';
import { Observable, of, EMPTY, throwError, BehaviorSubject, Subject, Subscription } from 'rxjs';
import { switchMap, tap, catchError, timeout, finalize, takeUntil, map, filter, take, shareReplay } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';


import { CodeGenerationIntroService, CodeIntroItem } from './code-generation-intro.service';
import { SSEService, SSEEvent } from './sse.service';
import { EnhancedSSEService } from './enhanced-sse.service';
import { FileModel } from '../components/code-viewer/code-viewer.component';
import { takeWhile } from 'rxjs/operators';
import { createLogger, createRegenerationLogger } from '../utils/logger';
import { StepperStateService } from './stepper-state.service';


/**
* Sequential Regeneration Service for Angular 19+
*
* Implements STRICTLY SEQUENTIAL execution for code generation regeneration:
* 1. Intro API executes first (sequential)
* 2. Regenerate/code API executes second (sequential, after intro completes)
* 3. SSE monitoring executes third (sequential, IMMEDIATELY after regenerate/code completes)
*
* Features:
* - Uses Angular 19+ patterns with inject(), Signals, and takeUntilDestroyed()
* - Filters SSE events specifically for 'code-regen' event type
* - Extracts file data from SSE metadata for Monaco editor updates
* - Maintains strict isolation from first-cut generation process
* - Provides comprehensive logging and error handling
* - Ensures 100% backward compatibility with existing interfaces
*/


export interface SequentialRegenerationConfig {
 enableIntroAPI: boolean;
 enableSSE: boolean;
 timeoutMs: number;
 retryAttempts: number;
 sseEventType: 'code-regen' | 'initial-code-gen';
}


export interface SequentialRegenerationState {
 isExecuting: boolean;
 introCompleted: boolean;
 regenerationCompleted: boolean;
 sseConnected: boolean;
 hasErrors: boolean;
 startTime: number;
 currentPhase: 'intro' | 'regeneration' | 'sse' | 'sse-monitoring' | 'completed' | 'error' | 'sse-waiting';
}


export interface SequentialRegenerationResult {
 overallSuccess: boolean;
 introResult?: {
   success: boolean;
   text?: string;
   error?: any;
   typewriterCompleted?: boolean;
 };
 regenerationResult?: {
   success: boolean;
   data?: any;
   error?: any;
   jobId?: string;
   projectId?: string;
 };
 sseResult?: {
   success: boolean;
   events?: SSEEvent[];
   error?: any;
   timeout?: boolean;
   reason?: string;
 };
 executionTime: number;
 filesUpdated?: FileModel[];
 timeout?: boolean;
 reason?: string;
}


export interface RegenerationAPIResponse {
 job_id?: string;
 project_id?: string;
 request_id?: string; // Explicitly ignored
 [key: string]: any;
}


export interface SSEFileData {
 fileName: string;
 content: string;
}


/**
* Sequential Regeneration Service
*
* ENHANCED: SSE Data Filtering for Regeneration
* - Only extracts progress, status, and metadata from SSE events
* - Does NOT extract log data or progress_description during regeneration
* - Prevents chat-window content updates during SSE listening
* - Enhanced connection management with longer timeouts and better reconnection
* - SSE listening starts IMMEDIATELY after /regenerate/code API completion (no delay)
*/
@Injectable({
 providedIn: 'root'
})
export class SequentialRegenerationService {
 private readonly logger = createLogger('SequentialRegenerationService');
 private readonly destroyRef = inject(DestroyRef);
 private readonly ngZone = inject(NgZone);
 private readonly codeGenerationIntroService = inject(CodeGenerationIntroService);
 private readonly sseService = inject(SSEService);
 private readonly enhancedSSEService = inject(EnhancedSSEService);
 private readonly stepperStateService = inject(StepperStateService);

 // ENHANCED: Unique regeneration session management
 private currentRegenerationSessionId: string | null = null;
 private activeRegenerationSessions = new Map<string, {
   sessionId: string;
   projectId: string;
   jobId: string;
   startTime: number;
   status: 'active' | 'completed' | 'failed';
 }>();

 // ENHANCED: Store current project and job IDs for retry functionality
 private currentProjectId: string | null = null;
 private currentJobId: string | null = null;

 // Cache for active SSE monitoring streams to prevent duplicates with session context
 private activeSSEStreams = new Map<string, {
   observable: Observable<any>;
   sessionId: string;
   projectId: string;
   jobId: string;
 }>();

 // CRITICAL FIX: Track processed SSE event IDs to prevent duplicates with session context
 private processedEventIds = new Map<string, Set<string>>(); // sessionId -> Set<eventId>
 private lastProcessedEventId: string | null = null;


 // Default configuration - ENHANCED: Increased timeout to prevent SSE connection errors
 private readonly defaultConfig: SequentialRegenerationConfig = {
   enableIntroAPI: true,
   enableSSE: true,
   timeoutMs: 600000, // INCREASED: 10 minutes timeout to prevent SSE connection errors
   retryAttempts: 2,
   sseEventType: 'code-regen'
 };


 // Reactive state management using Angular 19+ patterns
 private readonly executionState = new BehaviorSubject<SequentialRegenerationState>({
   isExecuting: false,
   introCompleted: false,
   regenerationCompleted: false,
   sseConnected: false,
   hasErrors: false,
   startTime: 0,
   currentPhase: 'intro'
 });


 // SSE event stream for file updates
 private readonly fileUpdates = new Subject<FileModel[]>();
 private readonly progressUpdates = new Subject<{
   progress: string;
   status: string;
   // ENHANCED: Error handling properties for FAILED events
   errorMessage?: string; // Extracted error message from log data
   log?: any; // Original log data for debugging
   url?: string;
   metadata?: any;
   deploymentCompleted?: boolean;
   event?: string; // Add event type for filtering
   introMessage?: string; // Add intro message handling (only for intro API, not SSE)
   codeGenerationStarted?: boolean; // Track CODE_GENERATION phase
   messageId?: string; // FIXED: Add messageId for standalone intro cards
   projectId?: string;
   jobId?: string;
   // ENHANCED: Phase-specific properties for dynamic loading states and tab management
   isCodeGeneration?: boolean; // True when progress is CODE_GENERATION
   isBuildPhase?: boolean; // True when progress is BUILD
   isDeployPhase?: boolean; // True when progress is DEPLOY
   isInProgress?: boolean; // True when status is IN_PROGRESS
   isCompleted?: boolean; // True when status is COMPLETED
   isFailed?: boolean; // True when status is FAILED
 }>();

 // ENHANCED: Progress descriptions for AI cards
 private readonly progressDescriptions = new Subject<{
   sessionId: string;
   progressPhase: 'intro' | 'code-generation' | 'build' | 'deploy';
   progressStatus: 'in-progress' | 'completed' | 'failed';
   description: string;
   event: string;
 }>();

 // Public observables
 readonly executionState$ = this.executionState.asObservable();
 readonly fileUpdates$ = this.fileUpdates.asObservable();
 readonly progressUpdates$ = this.progressUpdates.asObservable();
 readonly progressDescriptions$ = this.progressDescriptions.asObservable();


 /**
  * ENHANCED: Generate unique regeneration session ID
  */
 private generateRegenerationSessionId(): string {
   const timestamp = Date.now();
   const random = Math.random().toString(36).substring(2, 11);
   return `regen_session_${timestamp}_${random}`;
 }

 /**
  * ENHANCED: Start new regeneration session
  */
 private startRegenerationSession(projectId: string, jobId: string): string {
   const sessionId = this.generateRegenerationSessionId();

   // ENHANCED: Store current project and job IDs for retry functionality
   this.currentProjectId = projectId;
   this.currentJobId = jobId;

   // Clean up any existing session for this project/job
   const existingSessionKey = `${projectId}-${jobId}`;
   for (const [key, session] of this.activeRegenerationSessions.entries()) {
     if (session.projectId === projectId && session.jobId === jobId) {
       this.logger.info('🧹 Cleaning up existing regeneration session:', { sessionId: session.sessionId });
       this.activeRegenerationSessions.delete(key);
       this.processedEventIds.delete(session.sessionId);
     }
   }

   // Create new session
   this.activeRegenerationSessions.set(sessionId, {
     sessionId,
     projectId,
     jobId,
     startTime: Date.now(),
     status: 'active'
   });

   // Initialize event tracking for this session
   this.processedEventIds.set(sessionId, new Set<string>());
   this.currentRegenerationSessionId = sessionId;

   this.logger.info('🚀 Started new regeneration session:', {
     sessionId,
     projectId,
     jobId,
     activeSessionsCount: this.activeRegenerationSessions.size
   });

   return sessionId;
 }

 /**
  * ENHANCED: Complete regeneration session
  */
 private completeRegenerationSession(sessionId: string, status: 'completed' | 'failed'): void {
   const session = this.activeRegenerationSessions.get(sessionId);
   if (session) {
     session.status = status;
     this.logger.info('✅ Completed regeneration session:', { sessionId, status });

     // Clean up after a delay to allow for final processing
     setTimeout(() => {
       this.activeRegenerationSessions.delete(sessionId);
       this.processedEventIds.delete(sessionId);
       if (this.currentRegenerationSessionId === sessionId) {
         this.currentRegenerationSessionId = null;
       }
       this.logger.info('🧹 Cleaned up regeneration session:', { sessionId });
     }, 5000);
   }
 }

 /**
  * DISABLED: Emit progress description for AI card creation (using accordions instead)
  */
 private emitProgressDescription(
   progressPhase: 'intro' | 'code-generation' | 'build' | 'deploy',
   progressStatus: 'in-progress' | 'completed' | 'failed',
   description: string
 ): void {
   // DISABLED: We're using accordions for regeneration tracking instead of AI cards
   this.logger.info('📝 Progress description disabled - using accordions for regeneration tracking:', {
     progressPhase,
     progressStatus,
     description: description.substring(0, 100)
   });
 }

 /**
  * Execute sequential regeneration for code generation
  *
  * @param userRequest - User's regeneration request
  * @param codeFiles - Current code files
  * @param regenerationAPICall - Observable for the main regeneration API
  * @param projectId - Project ID for SSE monitoring
  * @param jobId - Job ID for SSE monitoring
  * @param targetMessageId - Optional message ID for text replacement
  * @param config - Optional configuration overrides
  * @returns Observable<SequentialRegenerationResult>
  */
 executeSequentialCodeRegeneration(
   userRequest: string,
   codeFiles: FileModel[],
   regenerationAPICall: Observable<any>,
   projectId: string,
   jobId: string,
   targetMessageId?: string,
   config?: Partial<SequentialRegenerationConfig>
 ): Observable<SequentialRegenerationResult> {
   // ENHANCED: Start new regeneration session for unique tracking
   const sessionId = this.startRegenerationSession(projectId, jobId);

   const finalConfig = { ...this.defaultConfig, ...config };
   const regenLogger = createRegenerationLogger('SequentialRegenerationService', 'executeSequentialCodeRegeneration');

   regenLogger.info('🚀 Starting SEQUENTIAL code regeneration execution', {
     sessionId,
     userRequest: userRequest.substring(0, 100),
     codeFilesCount: codeFiles.length,
     enableIntroAPI: finalConfig.enableIntroAPI,
     enableSSE: finalConfig.enableSSE,
     sseEventType: finalConfig.sseEventType,
     targetMessageId,
     timestamp: new Date().toISOString(),
     executionMode: 'SEQUENTIAL'
   });


   this.logger.info('🚀 Starting SEQUENTIAL code regeneration execution', {
     userRequest: userRequest.substring(0, 100),
     codeFilesCount: codeFiles.length,
     enableIntroAPI: finalConfig.enableIntroAPI,
     enableSSE: finalConfig.enableSSE,
     sseEventType: finalConfig.sseEventType,
     targetMessageId,
     timestamp: new Date().toISOString(),
     executionMode: 'SEQUENTIAL'
   });


   // CRITICAL: Mark regeneration as active to isolate stepper
   this.stepperStateService.setRegenerationActive(true);
   this.logger.info('🔒 Stepper isolated - regeneration active state set');


   // Initialize execution state
   this.updateExecutionState({
     isExecuting: true,
     introCompleted: false,
     regenerationCompleted: false,
     sseConnected: false,
     hasErrors: false,
     startTime: Date.now(),
     currentPhase: 'intro'
   });


   // Execute sequential phases with session context
   return this.executeSequentialPhases(
     userRequest,
     codeFiles,
     regenerationAPICall,
     projectId,
     jobId,
     sessionId,
     targetMessageId,
     finalConfig
   ).pipe(
     finalize(() => {
       this.logger.info('🏁 Sequential regeneration finalize block - ensuring complete cleanup');
       this.updateExecutionState({
         isExecuting: false,
         currentPhase: 'completed'
       });
       // Complete the regeneration session
       this.completeRegenerationSession(sessionId, 'completed');
       // CRITICAL FIX: Reset stepper state to re-enable for future regenerations
       this.stepperStateService.setRegenerationActive(false);
       this.logger.info('🔓 Stepper re-enabled after regeneration completion');
     }),
     catchError(error => {
       this.logger.error('❌ Sequential regeneration error in catchError block:', error);
       this.completeRegenerationSession(sessionId, 'failed');
       // CRITICAL FIX: Reset stepper state on error to re-enable for future regenerations
       this.stepperStateService.setRegenerationActive(false);
       this.logger.info('🔓 Stepper re-enabled after regeneration error');
       throw error;
     }),
     takeUntilDestroyed(this.destroyRef)
   );
 }


 /**
  * Execute the three sequential phases with session context
  */
 private executeSequentialPhases(
   userRequest: string,
   codeFiles: FileModel[],
   regenerationAPICall: Observable<any>,
   projectId: string,
   jobId: string,
   sessionId: string,
   targetMessageId?: string,
   config?: SequentialRegenerationConfig
 ): Observable<SequentialRegenerationResult> {
   const startTime = Date.now();
   let introResult: any = null;
   let regenerationResult: any = null;
   let sseResult: any = null;
   const regenLogger = createRegenerationLogger('SequentialRegenerationService', 'executeSequentialPhases');


   regenLogger.info('🔄 Starting PHASE 1: Intro API execution');
   this.logger.info('🔄 Starting PHASE 1: Intro API execution');


   // PHASE 1: Execute intro API sequentially
   const introExecution = config?.enableIntroAPI
     ? this.executeIntroPhase(userRequest, codeFiles, targetMessageId)
     : of({ success: true, text: 'Preparing your regeneration...' });


   // Execute all phases sequentially and return the final result
   return of(null).pipe(
     switchMap(() => introExecution),
     tap((introRes: any) => {
       introResult = introRes;
       this.logger.info('✅ PHASE 1 completed: Intro API', {
         success: introRes.success,
         textLength: introRes.text?.length || 0
       });
       this.updateExecutionState({
         introCompleted: true,
         currentPhase: 'regeneration'
       });


       this.logger.info('🔄 Starting PHASE 2: Regeneration API execution');
     }),
     switchMap(() => {
       // PHASE 2: Execute regeneration API after intro completes
       return regenerationAPICall.pipe(
         timeout(config?.timeoutMs || 30000),
         tap((result: RegenerationAPIResponse) => {
           // ENHANCED: Extract ONLY job_id and project_id, explicitly ignore request_id
           const extractedData = this.extractRegenerationAPIData(result);
           regenerationResult = {
             success: true,
             data: result,
             jobId: extractedData.jobId,
             projectId: extractedData.projectId
           };


           this.logger.info('✅ PHASE 2 completed: Regeneration API - job started', {
             hasProjectId: !!extractedData.projectId,
             hasJobId: !!extractedData.jobId,
             ignoredRequestId: !!result.request_id,
             note: 'Regeneration job started, waiting for SSE events'
           });


           // Update project and job IDs for SSE phase
           if (extractedData.projectId && extractedData.jobId) {
             this.logger.info('🆔 Updated project/job IDs for SSE phase:', {
               projectId: extractedData.projectId,
               jobId: extractedData.jobId
             });
           }


           // CRITICAL: Do NOT mark regeneration as completed - only job started
           // Regeneration is completed ONLY when DEPLOY + COMPLETED from SSE
           this.updateExecutionState({
             regenerationCompleted: false, // Keep false until DEPLOY COMPLETED from SSE
             currentPhase: 'sse-waiting'
           });


           // DO NOT emit progress update for regeneration API completion
           // This prevents intermediate completion events
           this.logger.info('🔄 Regeneration API completed - job started, waiting for SSE events...');
         }),
         catchError(error => {
           regenerationResult = { success: false, error };
           this.logger.error('❌ PHASE 2 failed: Regeneration API', error);
           return throwError(() => error);
         })
       );
     }),
     switchMap(regeneration => {
       this.logger.info('🔄 Starting PHASE 3: SSE monitoring execution');


       // PHASE 3: Start SSE monitoring after regeneration API completes
       if (config?.enableSSE) {
         // ENHANCED: Use job_id from regeneration API response if available
         const sseJobId = regenerationResult?.jobId || jobId;
         const sseProjectId = regenerationResult?.projectId || projectId;

         // Check if a stream for this job already exists to prevent duplicates
         if (this.activeSSEStreams.has(sseJobId)) {
           this.logger.info(`🔌 Reusing existing SSE monitoring stream for job: ${sseJobId}`);
           return this.activeSSEStreams.get(sseJobId)!.observable;
         }

         this.logger.info('🔌 Starting new SSE monitoring for REGENERATION with IDs:', {
           jobId: sseJobId,
           projectId: sseProjectId,
           eventType: config.sseEventType,
           usingExtractedIds: !!regenerationResult?.jobId
         });

         // CRITICAL FIX: Start SSE listening immediately after regenerate/code API completion (no delay)
         const newSSEStream = this.executeSSEPhase(sseProjectId, sseJobId, sessionId, config.sseEventType).pipe(
           tap(result => {
             sseResult = result;
             this.logger.info('✅ PHASE 3 completed: SSE monitoring for regeneration', {
               success: result.success,
               eventsReceived: result.events?.length || 0,
               note: 'Regeneration completion determined by DEPLOY+COMPLETED from SSE'
             });
             this.updateExecutionState({
               sseConnected: true,
               // Do NOT mark as completed here - only when DEPLOY+COMPLETED from SSE
               currentPhase: 'sse-monitoring'
             });
           }),
           finalize(() => {
             this.logger.info(`🧹 Cleaning up SSE stream cache for job: ${sseJobId}`);
             // Clean up the stream from cache
             const streamInfo = this.activeSSEStreams.get(sseJobId);
             if (streamInfo && streamInfo.sessionId === sessionId) {
               this.activeSSEStreams.delete(sseJobId);
             }
           }),
           shareReplay(1) // Make it a hot, shareable observable that replays the last result
         );

         // Store the new stream in the cache before returning
         this.activeSSEStreams.set(sseJobId, {
           observable: newSSEStream,
           sessionId,
           projectId: sseProjectId,
           jobId: sseJobId
         });
         return newSSEStream;

       } else {
         sseResult = { success: true, events: [] };
         return of(regeneration);
       }
     }),
     switchMap(() => {
       // Combine all results
       const executionTime = Date.now() - startTime;
       const overallSuccess = introResult?.success && regenerationResult?.success && sseResult?.success;


       const result: SequentialRegenerationResult = {
         overallSuccess,
         introResult,
         regenerationResult,
         sseResult,
         executionTime,
         timeout: sseResult?.timeout || false,
         reason: sseResult?.reason
       };


       this.logger.info('🎉 Sequential regeneration completed', {
         overallSuccess,
         executionTime,
         introSuccess: introResult?.success,
         regenerationSuccess: regenerationResult?.success,
         sseSuccess: sseResult?.success
       });


       return of(result);
     }),
     catchError(error => {
       const executionTime = Date.now() - startTime;
       this.logger.error('❌ Sequential regeneration failed - performing cleanup', {
         error: error instanceof Error ? error.message : String(error),
         executionTime,
         introSuccess: introResult?.success,
         regenerationSuccess: regenerationResult?.success,
         sseSuccess: sseResult?.success,
         phase: 'error-recovery'
       });

       // ENHANCED: Perform comprehensive cleanup on failure
       this.performFailureCleanup();

       this.updateExecutionState({
         hasErrors: true,
         currentPhase: 'error'
       });

       return of({
         overallSuccess: false,
         introResult,
         regenerationResult: regenerationResult || { success: false, error },
         sseResult,
         executionTime
       });
     })
   );
 }


 /**
  * Execute intro phase with standalone intro cards
  * FIXED: Creates standalone intro cards instead of using shared state
  */
 private executeIntroPhase(
   userRequest: string,
   codeFiles: FileModel[],
   targetMessageId?: string
 ): Observable<{ success: boolean; text?: string; error?: any; messageId?: string }> {
   const regenLogger = createRegenerationLogger('SequentialRegenerationService', 'executeIntroPhase');

   regenLogger.info('🎭 Executing intro phase for regeneration with standalone intro cards');
   this.logger.info('🎭 Executing intro phase for regeneration with standalone intro cards');

   // ENHANCED: Emit progress description for intro phase start
   this.emitProgressDescription('intro', 'in-progress', 'Preparing your regeneration request...');

   // FIXED: Use the simpler createStandaloneIntroCard method
   return this.codeGenerationIntroService.createStandaloneIntroCard(userRequest, codeFiles).pipe(
     tap((result) => {
       this.logger.info('✅ Standalone intro card created successfully:', {
         messageId: result.messageId,
         textLength: result.introText?.length || 0
       });

       // ENHANCED: Emit progress description for intro phase completion
       this.emitProgressDescription('intro', 'completed', 'Regeneration request prepared successfully');

       // FIXED: Emit intro message with messageId for standalone card tracking
       this.progressUpdates.next({
         progress: 'INTRO',
         status: 'COMPLETED',
         introMessage: result.introText,
         messageId: result.messageId,
         event: 'standalone-intro-card-created'
       });
     }),
     map((result) => ({
       success: true,
       text: result.introText,
       messageId: result.messageId
     })),
     catchError(error => {
       this.logger.error('❌ Standalone intro card creation failed', error);
       return of({
         success: false,
         text: 'Preparing your regeneration...',
         error,
         messageId: undefined
       });
     })
   );
 }





 /**
  * Extract job_id and project_id from regeneration API response
  * ENHANCED: Explicitly ignores request_id field to avoid confusion
  */
 private extractRegenerationAPIData(response: RegenerationAPIResponse): { jobId?: string; projectId?: string } {
   const extracted = {
     jobId: response.job_id,
     projectId: response.project_id
   };


   this.logger.info('🔍 Extracting regeneration API data:', {
     foundJobId: !!extracted.jobId,
     foundProjectId: !!extracted.projectId,
     ignoredRequestId: !!response.request_id,
     jobId: extracted.jobId,
     projectId: extracted.projectId
   });


   // Log warning if request_id is present but ignored
   if (response.request_id) {
     this.logger.warn('⚠️ Ignoring request_id field as per requirements:', response.request_id);
   }


   return extracted;
 }


 /**
  * Execute SSE phase with code-regen event filtering
  * ENHANCED: Uses job_id from regeneration API response and waits for actual deployment completion
  * UPDATED: Follows strict sequence - CODE_GENERATION → BUILD → DEPLOY
  * ENHANCED: Includes session context for unique regeneration tracking
  */
 private executeSSEPhase(
   projectId: string,
   jobId: string,
   sessionId: string,
   eventType: 'code-regen' | 'initial-code-gen'
 ): Observable<{ success: boolean; events?: SSEEvent[]; error?: any }> {
   const regenLogger = createRegenerationLogger('SequentialRegenerationService', 'executeSSEPhase');


   regenLogger.info('🔌 Executing SSE phase for regeneration', {
     projectId,
     jobId,
     sessionId,
     eventType
   });
   this.logger.info('🔌 Executing SSE phase for regeneration', {
     projectId,
     jobId,
     sessionId,
     eventType
   });


   const receivedEvents: SSEEvent[] = [];
   let deploymentCompleted = false;
   let codeGenerationStarted = false;


   // ENHANCED: Configure SSE connection with session-specific options for regeneration
   const sessionKey = `${projectId}-${jobId}`;
   const cachedEventId = this.sseService.getCachedEventId(sessionKey);

   // ENHANCED: SSE options with session-specific headers and regeneration context
   const sseOptions = {
     reconnectInterval: 5000, // 5 second reconnect interval
     maxReconnectAttempts: 20, // More reconnect attempts for regeneration
     enableExponentialBackoff: true,
     backoffFactor: 1.5,
     maxBackoffInterval: 30000,
     enableHeartbeat: true,
     heartbeatInterval: 30000, // 30 second heartbeat
     lastEventId: cachedEventId || undefined, // Add cached event ID for checkpointing
     useHeadersForEventId: true, // NEW: Use headers for last-event-id (polyfill)
     generationType: 'code-regen' as const, // CRITICAL: Set generation type for regeneration
     enableSinceParameter: true, // Enable since parameter to prevent event replay
     customHeaders: {
       'Cache-Control': 'no-cache',
       'Accept': 'text/event-stream',
       'X-Regeneration-Session': sessionId, // Use unique session ID for tracking
       'X-Project-Job-Key': sessionKey // Keep project-job key for compatibility
     }
   };

   this.logger.info('🔧 SSE connection options for regeneration:', {
     sessionKey,
     hasCachedEventId: !!cachedEventId,
     cachedEventId: cachedEventId || 'none',
     jobId,
     projectId,
     generationType: sseOptions.generationType,
     enableSinceParameter: sseOptions.enableSinceParameter,
     note: 'Using code-regen type with since parameter to prevent event replay'
   });


   // ENHANCED: Comprehensive cleanup of all existing SSE connections before regeneration
   this.logger.info('🧹 Preparing for regeneration - comprehensive connection cleanup', {
     isCurrentlyConnected: this.sseService.isConnected(),
     jobId: jobId,
     projectId: projectId,
     generationType: 'code-regen',
     activeSSEStreams: this.activeSSEStreams.size,
     enhancedSSEStats: this.enhancedSSEService.getConnectionStatistics()
   });

   // 1. Clean up local SSE stream cache first
   if (this.activeSSEStreams.size > 0) {
     this.logger.info('🧹 Cleaning up local SSE stream cache:', {
       streamCount: this.activeSSEStreams.size,
       streamKeys: Array.from(this.activeSSEStreams.keys())
     });
     this.activeSSEStreams.clear();
   }

   // 2. Use enhanced SSE service preparation method for comprehensive cleanup
   this.enhancedSSEService.prepareForRegeneration(projectId, jobId);

   // 3. Additional safety check - ensure base SSE service is also disconnected
   if (this.sseService.isConnected()) {
     this.logger.info('🧹 Additional cleanup: Disconnecting base SSE service for regeneration');
     this.sseService.disconnect();
   }

   // 4. Reset any local regeneration state
   this.processedEventIds.clear();
   this.lastProcessedEventId = null;

   this.logger.info('✅ Comprehensive regeneration preparation completed:', {
     activeSSEStreams: this.activeSSEStreams.size,
     processedEventIds: this.processedEventIds.size,
     enhancedSSEStats: this.enhancedSSEService.getConnectionStatistics(),
     baseSSEConnected: this.sseService.isConnected()
   });

   // OPTIMIZATION: Use enhanced SSE service with code-regen generation type
   const connectionKey = `${projectId}-${jobId}`;
   const sseObservable = this.enhancedSSEService.startMonitoring(
     projectId,
     jobId,
     sseOptions,
     undefined, // No fallback callback
     'code-regen' // OPTIMIZATION: Specify regeneration type (no since=0 parameter)
   );

   // ENHANCED: Return observable with subscription tracking capability
   return sseObservable.pipe(
     tap(event => {
       this.logger.info('📨 Received SSE event for regeneration', {
         id: event.id,
         event: event.event,
         eventType: event.event,
         dataLength: event.data?.length || 0,
         jobId: jobId,
         expectedEventType: eventType,
         timestamp: new Date().toISOString()
       });


       // CRITICAL: ENHANCED EVENT FILTERING - Only process events that match the expected type
       // During regeneration, COMPLETELY IGNORE 'initial-code-gen' events to prevent interference
       // ENHANCED: Also validate event ID and data structure
       const shouldProcessEvent = this.validateSSEEvent(event, eventType);


       if (shouldProcessEvent) {
         // CRITICAL FIX: Check for duplicate events using enhanced validation
         if (this.isDuplicateEvent(event)) {
           this.logger.debug('⏭️ Skipping duplicate SSE event:', {
             id: event.id,
             eventType: event.event,
             reason: 'Already processed this event ID'
           });
           return;
         }


         receivedEvents.push(event);


         this.logger.info('🔍 Processing validated SSE event for regeneration:', {
           id: event.id,
           eventType: event.event,
           expectedType: eventType,
           isRegenerationContext: eventType === 'code-regen',
           processedEventsCount: this.processedEventIds.size,
           hasValidData: !!event.data
         });


         // Parse event data to check for deployment completion and extract code files
         try {
           const eventData = JSON.parse(event.data);
           this.logger.info('🔍 Processing SSE event data:', {
             progress: eventData.progress,
             status: eventData.status,
             eventType: event.event,
             hasUrl: !!eventData.url,
             hasMetadata: !!eventData.metadata
           });


           // UPDATED: Handle CODE_GENERATION phase first - Extract files on COMPLETED
           if (eventData.progress === 'CODE_GENERATION' && event.event === 'code-regen') {
             if (eventData.status === 'IN_PROGRESS' && !codeGenerationStarted) {
               this.logger.info('🚀 CODE_GENERATION started - regeneration process initiated');
               codeGenerationStarted = true;

               // ENHANCED: Emit progress description for code generation start
               this.emitProgressDescription('code-generation', 'in-progress', 'Generating your updated code...');
               // DO NOT emit progress updates for intermediate phases
               this.logger.info('📝 Code generation phase started - waiting for completion');
             } else if (eventData.status === 'COMPLETED') {
               this.logger.info('✅ CODE_GENERATION completed - extracting files from metadata');

               // ENHANCED: Emit progress description for code generation completion
               this.emitProgressDescription('code-generation', 'completed', 'Code generation completed successfully');

               // CRITICAL: Extract files from CODE_GENERATION COMPLETED (not BUILD phase)
               this.extractAndUpdateCodeFiles(eventData);

               // CRITICAL: Emit progress update for regeneration integration service
               this.progressUpdates.next({
                 progress: 'CODE_GENERATION',
                 status: 'COMPLETED',
                 metadata: eventData.metadata,
                 event: 'code-regen', // CRITICAL: Use 'code-regen' event type for regeneration integration service
                 deploymentCompleted: false
               });

               this.logger.info('📝 Code generation phase completed with files extracted and progress emitted');
             }
           }


           // UPDATED: BUILD phase processing (no file extraction needed here)
           if (eventData.progress === 'BUILD' &&
               (eventData.status === 'IN_PROGRESS' || eventData.status === 'COMPLETED') &&
               event.event === 'code-regen' && codeGenerationStarted) {
             this.logger.info('🔨 BUILD phase detected - processing build');

             // ENHANCED: Emit progress descriptions for build phase
             if (eventData.status === 'IN_PROGRESS') {
               this.emitProgressDescription('build', 'in-progress', 'Building your updated application...');
             } else if (eventData.status === 'COMPLETED') {
               this.emitProgressDescription('build', 'completed', 'Application build completed successfully');
             }

             // Files already extracted from CODE_GENERATION COMPLETED
             this.logger.info('📝 Build phase processing - files already available');
           }


           // ENHANCED: Handle DEPLOY phase with progress descriptions
           if (eventData.progress === 'DEPLOY' && event.event === 'code-regen' && codeGenerationStarted) {
             if (eventData.status === 'IN_PROGRESS') {
               this.logger.info('🚀 DEPLOY IN_PROGRESS - deployment started');
               this.emitProgressDescription('deploy', 'in-progress', 'Deploying your updated application...');
             } else if (eventData.status === 'COMPLETED') {
               deploymentCompleted = true;
               this.logger.info('🎉 DEPLOY COMPLETED for code-regen event - REGENERATION FINALLY COMPLETED');

               // ENHANCED: Emit progress description for deployment completion
               this.emitProgressDescription('deploy', 'completed', 'Application deployed successfully');

               // ENHANCED: Cache event ID for checkpointing
               if (event.id) {
                 this.cacheEventIdForCheckpointing(event.id, projectId, jobId);
               }
             }
           }

           // CRITICAL: Check for deployment completion - ONLY completion trigger
           if (eventData.progress === 'DEPLOY' && eventData.status === 'COMPLETED' &&
               event.event === 'code-regen' && codeGenerationStarted) {

             // CRITICAL: Mark regeneration as completed ONLY here - this is the SINGLE completion point
             this.updateExecutionState({
               regenerationCompleted: true,
               currentPhase: 'completed'
             });


             // CRITICAL: Reset regeneration state to re-enable stepper for future operations
             this.stepperStateService.setRegenerationActive(false);
             this.logger.info('🔓 Stepper re-enabled - regeneration completed');


             // Emit FINAL completion event - this is the ONLY completion event
             this.progressUpdates.next({
               progress: 'DEPLOY',
               status: 'COMPLETED',
               // REMOVED: log, dynamicMessage - no longer extracted for regeneration
               url: eventData.url,
               event: 'code-regen', // CRITICAL: Use 'code-regen' event type for regeneration integration service
               deploymentCompleted: true,
               // ENHANCED: Include metadata for accordion creation
               metadata: eventData.metadata
             });
           }


           this.processSSEEvent(event);
         } catch (error) {
           this.logger.warn('⚠️ Failed to parse SSE event data:', error);
           this.processSSEEvent(event);
         }
       } else {
         // ENHANCED: Log ignored events with more detail for debugging
         this.logger.info('⏭️ IGNORING SSE event - strict regeneration filtering active', {
           received: event.event,
           expected: eventType,
           isRegenerationContext: eventType === 'code-regen',
           reason: eventType === 'code-regen' && event.event === 'initial-code-gen'
             ? 'Ignoring initial-code-gen during regeneration to prevent interference'
             : 'Event type mismatch',
           strictFiltering: true
         });
       }
     }),
     // ENHANCED: Add logging when SSE connection completes or closes
     tap({
       complete: () => {
         this.logger.info('🔚 SSE connection completed for regeneration', {
           jobId: jobId,
           eventType: eventType,
           deploymentCompleted: deploymentCompleted,
           eventsReceived: receivedEvents.length,
           timestamp: new Date().toISOString()
         });
       }
     }),
     // Wait until deployment is completed or timeout
     takeWhile(() => !deploymentCompleted, true),
     // ENHANCED: Increased timeout for regeneration SSE monitoring
     timeout({
       each: 600000, // 5 minute timeout for SSE monitoring (increased from 2 minutes)
       with: () => {
         this.logger.warn('⏰ SSE timeout reached after 5 minutes - checking if deployment was completed');

         // Check if we received any deployment completion events
         const hasDeploymentCompletion = receivedEvents.some(event => {
           try {
             const eventData = JSON.parse(event.data);
             return eventData.progress === 'DEPLOY' && eventData.status === 'COMPLETED';
           } catch {
             return false;
           }
         });

         if (hasDeploymentCompletion) {
           this.logger.info('✅ SSE timeout but deployment completion was detected - treating as success');
           deploymentCompleted = true;
           return of({
             success: true,
             events: receivedEvents,
             timeout: true,
             reason: 'deployment_completed'
           });
         } else {
           this.logger.warn('⚠️ SSE timeout without clear deployment completion - treating as timeout');
           deploymentCompleted = true;
           return of({
             success: false,
             events: receivedEvents,
             timeout: true,
             reason: 'timeout_without_completion'
           });
         }
       }
     }),
     catchError(error => {
       this.logger.error('❌ SSE phase failed with enhanced error handling', {
         error: error.message || error,
         errorType: error.name,
         eventsReceived: receivedEvents.length,
         deploymentCompleted,
         jobId,
         projectId
       });


       // Only emit fallback message for actual failures, not completion
       if (!deploymentCompleted) {
         this.progressUpdates.next({
           progress: 'SSE',
           status: 'FAILED',
           // REMOVED: log, dynamicMessage - no longer extracted for regeneration
           event: 'code-regen' // CRITICAL: Use 'code-regen' event type for regeneration integration service
         });
       }


       return of({ success: deploymentCompleted, error: deploymentCompleted ? null : error, events: receivedEvents });
     }),
     // Complete successfully after processing events
     switchMap(() => of({ success: true, events: receivedEvents })),
     // ENHANCED: Add cleanup when subscription ends
     finalize(() => {
       this.logger.info('🧹 SSE regeneration observable finalized - cleaning up connection:', {
         connectionKey,
         projectId,
         jobId,
         timestamp: new Date().toISOString()
       });

       // Update enhanced SSE service UI state to reflect completion
       this.enhancedSSEService.updateConnectionUIState(connectionKey, {
         isLoading: false,
         progressDescription: 'Completed'
       });
     })
   );
 }


 /**
  * Process individual SSE events for file updates and progress
  * ENHANCED: Handles all possible states (IN_PROGRESS, FAILED, DEPLOY, COMPLETED)
  */
 private processSSEEvent(event: SSEEvent): void {
   const regenLogger = createRegenerationLogger('SequentialRegenerationService', 'processSSEEvent');


   try {
     const eventData = JSON.parse(event.data);


     // ENHANCED: Log complete event data for debugging regeneration flow
     // MODIFIED: Only log progress, status, metadata - no log data or progress_description
     regenLogger.info('📨 RECEIVED SSE EVENT - Essential Data Only:', {
       eventType: event.event,
       eventId: event.id,
       status: eventData.status,
       progress: eventData.progress,
       hasMetadata: !!eventData.metadata,
       hasUrl: !!eventData.url,
       timestamp: new Date().toISOString()
     });


     this.logger.info('📨 RECEIVED SSE EVENT - Essential Data Only:', {
       eventType: event.event,
       eventId: event.id,
       status: eventData.status,
       progress: eventData.progress,
       hasMetadata: !!eventData.metadata,
       hasUrl: !!eventData.url,
       timestamp: new Date().toISOString()
     });


     // ENHANCED: Print extracted response data for each event
     this.logExtractedEventData(eventData, event.event ?? '');


     // FILTER: Suppress unwanted deployment success messages
     if (this.shouldSuppressMessage(eventData)) {
       this.logger.info('🚫 Suppressing unwanted deployment message:', eventData);
       return;
     }


     // ENHANCED: Extract intro message if present (but don't display in chat)
     const introMessage = this.extractIntroMessage(eventData);

     // ENHANCED: Check for initial code generation completion and handle SSE closure
     this.handleInitialCodeGenerationCompletion(event, eventData);

     // ENHANCED: PROGRESS UPDATE FILTERING FOR DYNAMIC LOADING STATES
     // Emit updates for intro messages AND all regeneration progress phases for UI state management
     const shouldEmitUpdate = (
       // Always emit intro messages
       introMessage ||
       // ENHANCED: Emit all regeneration progress phases for dynamic loading states and tab management
       (event.event === 'code-regen' && (
         // CODE_GENERATION phase (any status)
         (eventData.progress === 'CODE_GENERATION') ||
         // BUILD phase (any status)
         (eventData.progress === 'BUILD') ||
         // DEPLOY phase (any status)
         (eventData.progress === 'DEPLOY')
       ))
     );


     if (shouldEmitUpdate) {
       // ENHANCED: Include detailed progress information for dynamic loading states and tab management
       const progressUpdate = {
         progress: eventData.progress || '',
         status: eventData.status || '',
         url: eventData.url, // Include URL for deployment completion
         metadata: eventData.metadata, // Include metadata for file updates
         event: event.event, // Include event type for filtering
         introMessage: introMessage, // Include intro message (hidden from chat)
         deploymentCompleted: eventData.progress === 'DEPLOY' && eventData.status === 'COMPLETED' && event.event === 'code-regen',
         // ENHANCED: Add phase-specific information for UI state management
         isCodeGeneration: eventData.progress === 'CODE_GENERATION',
         isBuildPhase: eventData.progress === 'BUILD',
         isDeployPhase: eventData.progress === 'DEPLOY',
         isInProgress: eventData.status === 'IN_PROGRESS',
         isCompleted: eventData.status === 'COMPLETED',
         isFailed: eventData.status === 'FAILED'
       };

       this.progressUpdates.next(progressUpdate);


       this.logger.info('✅ EMITTED PROGRESS UPDATE:', {
         progress: eventData.progress,
         status: eventData.status,
         event: event.event,
         isIntroMessage: !!introMessage,
         isDeploymentCompleted: eventData.progress === 'DEPLOY' && eventData.status === 'COMPLETED'
       });
     } else {
       // ENHANCED: Log ALL intermediate events without emitting progress updates
       this.logger.info('📝 SSE EVENT PROCESSED (NO PROGRESS UPDATE EMITTED):', {
         progress: eventData.progress,
         status: eventData.status,
         event: event.event,
         reason: 'Intermediate event - waiting for DEPLOY+COMPLETED'
       });
     }


     // Process file updates for BUILD progress with IN_PROGRESS/COMPLETED status
     // OR for CODE_GENERATION progress with COMPLETED status (regeneration case)
     const shouldProcessFiles = (
       (eventData.progress === 'BUILD' && (eventData.status === 'IN_PROGRESS' || eventData.status === 'COMPLETED')) ||
       (eventData.progress === 'CODE_GENERATION' && eventData.status === 'COMPLETED' && event.event === 'code-regen')
     );

     if (shouldProcessFiles) {
       this.logger.info('📁 Processing files for regeneration:', {
         progress: eventData.progress,
         status: eventData.status,
         event: event.event,
         hasMetadata: !!eventData.metadata
       });
       this.extractAndEmitFileUpdates(eventData.metadata);
     }


     // REMOVED: Duplicate completion logic - completion is handled in executeSSEPhase only
     // This prevents duplicate completion events from being emitted


     // CRITICAL: Handle FAILED events - ONLY for code-regen events
     if ((eventData.status === 'FAILED' || eventData.progress === 'FAILED') && event.event === 'code-regen') {
       this.logger.error('❌ REGENERATION FAILED for code-regen event - regeneration failed');

       // ENHANCED: Extract error message from log data for error accordion display
       let errorMessage = 'Code regeneration failed. Please try again.';

       if (eventData.log) {
         try {
           // Handle different log data formats
           if (typeof eventData.log === 'string') {
             // Try to parse JSON log data
             if (eventData.log.includes('{') && eventData.log.includes('}')) {
               const parsedLog = JSON.parse(eventData.log);
               errorMessage = parsedLog.message || parsedLog.error || eventData.log;
             } else {
               errorMessage = eventData.log;
             }
           } else if (typeof eventData.log === 'object' && eventData.log.message) {
             errorMessage = eventData.log.message;
           }

           this.logger.info('✅ Extracted error message from SSE log data:', {
             originalLog: eventData.log,
             extractedMessage: errorMessage.substring(0, 100) + '...'
           });
         } catch (parseError) {
           this.logger.warn('⚠️ Failed to parse error message from log data, using default:', parseError);
           // Keep default error message
         }
       }

       // CRITICAL: Reset regeneration state to re-enable stepper after failure
       this.stepperStateService.setRegenerationActive(false);
       this.logger.info('🔓 Stepper re-enabled - regeneration failed');

       // Emit special failure event with extracted error message for regeneration integration service
       this.progressUpdates.next({
         progress: eventData.progress || 'FAILED',
         status: 'FAILED',
         errorMessage: errorMessage, // ENHANCED: Include extracted error message
         log: eventData.log, // ENHANCED: Include original log data for debugging
         event: 'code-regen', // CRITICAL: Use 'code-regen' event type for regeneration integration service
         deploymentCompleted: false,
         // ENHANCED: Include project and job IDs for retry functionality
         projectId: this.currentProjectId || undefined,
         jobId: this.currentJobId || undefined
       });
     }


     // ENHANCED: Handle all possible states
     this.handleSSEEventState(eventData);


   } catch (error) {
     this.logger.error('❌ Error processing SSE event', error);


     // ENHANCED: Emit fallback message for malformed event data
     this.progressUpdates.next({
       progress: 'PROCESSING',
       status: 'IN_PROGRESS',
       // REMOVED: log, dynamicMessage - no longer extracted for regeneration
       event: 'code-regen' // CRITICAL: Use 'code-regen' event type for regeneration integration service
     });
   }
 }


 /**
  * Log extracted event data for debugging regeneration flow
  * MODIFIED: Only log essential data - no log or progress_description
  */
 private logExtractedEventData(eventData: any, eventType: string): void {
   const extractedData = {
     eventType: eventType,
     progress: eventData.progress,
     status: eventData.status,
     // REMOVED: log, progressDescription - no longer extracted for regeneration
     url: eventData.url,
     metadata: eventData.metadata ? {
       hasFiles: !!eventData.metadata.files,
       fileCount: eventData.metadata.files ? Object.keys(eventData.metadata.files).length : 0,
       hasDeploymentUrl: !!eventData.metadata.deployment_url,
       deploymentUrl: eventData.metadata.deployment_url
     } : null,
     timestamp: new Date().toISOString()
   };


   this.logger.info('🔍 EXTRACTED EVENT DATA:', extractedData);


   // Log specific phase information
   if (eventData.progress === 'CODE_GENERATION') {
     this.logger.info('🏗️ CODE_GENERATION PHASE:', {
       status: eventData.status
       // REMOVED: description extraction - no longer needed for regeneration
     });
   } else if (eventData.progress === 'BUILD') {
     this.logger.info('🔨 BUILD PHASE:', {
       status: eventData.status,
       hasFiles: !!eventData.metadata?.files,
       fileCount: eventData.metadata?.files ? Object.keys(eventData.metadata.files).length : 0
     });
   } else if (eventData.progress === 'DEPLOY') {
     this.logger.info('🚀 DEPLOY PHASE:', {
       status: eventData.status,
       hasUrl: !!eventData.url,
       url: eventData.url?.substring(0, 50) + '...'
     });
   }
 }


 /**
  * Handle different SSE event states
  * ENHANCED: Comprehensive state handling for all possible states
  */
 private handleSSEEventState(eventData: any): void {
   const { status, progress } = eventData;


   switch (status) {
     case 'IN_PROGRESS':
       this.logger.info(`🔄 Regeneration in progress: ${progress}`);
       break;


     case 'DEPLOY':
       if (progress === 'BUILD') {
         this.logger.info('🏗️ Build completed, deploying application');
       } else if (progress === 'DEPLOY') {
         this.logger.info('🚀 Deployment in progress');
       }
       break;


     case 'COMPLETED':
       if (progress === 'DEPLOY') {
         this.logger.info('✅ Deployment completed - regeneration process finished');
       } else {
         this.logger.info(`✅ ${progress} phase completed successfully`);
       }
       break;


     case 'FAILED':
       this.logger.error(`❌ Regeneration failed at ${progress} phase`);
       // Emit failure event for UI handling
       this.progressUpdates.next({
         progress: progress || 'FAILED',
         status: 'FAILED',
         // REMOVED: log, dynamicMessage - no longer extracted for regeneration
         event: 'code-regen', // CRITICAL: Use 'code-regen' event type for regeneration integration service
         deploymentCompleted: false
       });
       break;


     default:
       this.logger.warn(`⚠️ Unknown SSE event status: ${status} at ${progress}`);
   }
 }


 /**
  * Generate dynamic progress message from SSE event data
  * ENHANCED: Replaces hardcoded messages with dynamic SSE-driven content
  */
 private generateDynamicProgressMessage(eventData: any): string {
   const { status, progress, log, progress_description } = eventData;


   // Use progress_description if available (highest priority)
   if (progress_description && progress_description.trim()) {
     return progress_description.trim();
   }


   // Use log if available (medium priority)
   if (log && log.trim()) {
     return log.trim();
   }


   // Generate message based on progress and status (fallback)
   return this.generateFallbackProgressMessage(progress, status);
 }


 /**
  * Extract intro message from SSE event data (but keep hidden from chat UI)
  * ENHANCED: Process intro messages without displaying them as AI messages
  */
 private extractIntroMessage(eventData: any): string | undefined {
   // Look for intro message in various possible fields
   const introFields = ['intro_message', 'introMessage', 'intro', 'message'];


   for (const field of introFields) {
     if (eventData[field] && typeof eventData[field] === 'string') {
       this.logger.info('📝 Extracted intro message from SSE event (hidden from chat):', {
         field,
         messageLength: eventData[field].length,
         preview: eventData[field].substring(0, 50) + '...'
       });
       return eventData[field];
     }
   }


   return undefined;
 }


 /**
  * Generate fallback progress message when SSE data is insufficient
  * ENHANCED: Dynamic message generation based on progress and status
  */
 private generateFallbackProgressMessage(progress: string, status: string): string {
   const progressUpper = (progress || '').toUpperCase();
   const statusUpper = (status || '').toUpperCase();


   if (progressUpper === 'BUILD') {
     switch (statusUpper) {
       case 'IN_PROGRESS':
         return 'Building your updated application...';
       case 'DEPLOY':
         return 'Build completed! Preparing for deployment...';
       case 'COMPLETED':
         return 'Build completed successfully!';
       case 'FAILED':
         return 'Build encountered issues. Please try again.';
       default:
         return 'Building your application...';
     }
   } else if (progressUpper === 'DEPLOY') {
     switch (statusUpper) {
       case 'IN_PROGRESS':
         return 'Deploying your updated application...';
       case 'COMPLETED':
         return 'Deployment completed! Your changes are now live.';
       case 'FAILED':
         return 'Deployment failed. Please try again.';
       default:
         return 'Deploying your application...';
     }
   } else if (progressUpper === 'OVERVIEW') {
     return 'Analyzing your code changes...';
   }


   // Default fallback
   return `Processing: ${progress || 'your request'}...`;
 }


 /**
  * Extract file data from SSE metadata and emit updates
  */
 private extractAndEmitFileUpdates(metadata: any[]): void {
   if (!metadata || !Array.isArray(metadata)) {
     this.logger.debug('⚠️ No metadata array found in SSE event');
     return;
   }


   // Look for metadata with type: "metadata" containing file array
   const metadataItem = metadata.find(item => item.type === 'metadata');
   if (!metadataItem || !metadataItem.data) {
     this.logger.debug('⚠️ No metadata item with type "metadata" found');
     return;
   }


   let files: any[] = [];


   // Handle different data structures
   if (Array.isArray(metadataItem.data)) {
     files = metadataItem.data;
   } else if (metadataItem.data.files && Array.isArray(metadataItem.data.files)) {
     files = metadataItem.data.files;
   }


   if (files.length === 0) {
     this.logger.debug('⚠️ No files found in metadata');
     return;
   }


   // Convert to FileModel format
   const fileModels: FileModel[] = files.map(file => ({
     name: file.fileName || file.path || 'Unknown file',
     type: 'file',
     content: file.content || file.code || '',
     fileName: file.fileName || file.path || 'Unknown file'
   }));


   this.logger.info('✅ Extracted files from SSE metadata', {
     fileCount: fileModels.length,
     files: fileModels.map(f => `${f.name} (${f.content?.length || 0} chars)`)
   });


   // Emit file updates
   this.fileUpdates.next(fileModels);
 }


 /**
  * Extract and update code files from SSE metadata during BUILD phase or CODE_GENERATION COMPLETED
  * ENHANCED: Extract code files from metadata.data where type is 'files' for both BUILD and CODE_GENERATION events
  */
 private extractAndUpdateCodeFiles(eventData: any): void {
   const eventType = eventData.progress === 'CODE_GENERATION' ? 'CODE_GENERATION COMPLETED' : 'BUILD phase';
   this.logger.info(`🔨 Extracting code files from ${eventType} SSE event`);


   if (!eventData.metadata || !Array.isArray(eventData.metadata)) {
     this.logger.warn('⚠️ No metadata array found in SSE event data');
     return;
   }


   // Find the files metadata entry
   const filesMetadata = eventData.metadata.find((item: any) => item.type === 'files');
   if (!filesMetadata || !Array.isArray(filesMetadata.data)) {
     this.logger.warn('⚠️ No files metadata found in SSE event');
     return;
   }


   this.logger.info('📁 Found files metadata:', {
     fileCount: filesMetadata.data.length,
     files: filesMetadata.data.map((f: any) => f.fileName || f.name || 'Unknown')
   });


   // Convert to FileModel format
   const extractedFiles: FileModel[] = filesMetadata.data.map((file: any) => ({
     fileName: file.fileName || file.name || 'Unknown file',
     name: file.fileName || file.name || 'Unknown file',
     content: file.content || file.code || '',
     language: this.detectLanguageFromFileName(file.fileName || file.name || ''),
     path: file.path || file.fileName || file.name || 'Unknown file'
   }));


   // this.logger.info('✅ Extracted code files for Monaco editor update:', {
   //   fileCount: extractedFiles.length,
   //   files: extractedFiles.map(f => `${f.fileName} (${f.content.length} chars)`)
   // });


   // Emit file updates for Monaco editor
   this.fileUpdates.next(extractedFiles);
   this.logger.info('✅ File updates emitted via fileUpdates$ subject:', {
     fileCount: extractedFiles.length,
     files: extractedFiles.map(f => f.fileName),
     timestamp: new Date().toISOString()
   });


   // Emit progress update with correct event type for regeneration integration service
   this.progressUpdates.next({
     progress: eventData.progress === 'CODE_GENERATION' ? 'CODE_GENERATION' : 'BUILD',
     status: eventData.status,
     // REMOVED: log - no longer extracted for regeneration
     metadata: extractedFiles,
     event: 'code-regen' // CRITICAL: Use 'code-regen' event type for regeneration integration service
   });
 }


 /**
  * Detect programming language from file name
  */
 private detectLanguageFromFileName(fileName: string): string {
   const extension = fileName.split('.').pop()?.toLowerCase();
   const languageMap: { [key: string]: string } = {
     'js': 'javascript',
     'ts': 'typescript',
     'jsx': 'javascript',
     'tsx': 'typescript',
     'html': 'html',
     'css': 'css',
     'scss': 'scss',
     'json': 'json',
     'md': 'markdown',
     'py': 'python',
     'java': 'java',
     'cpp': 'cpp',
     'c': 'c'
   };
   return languageMap[extension || ''] || 'plaintext';
 }


 /**
  * Update execution state
  */
 private updateExecutionState(updates: Partial<SequentialRegenerationState>): void {
   const currentState = this.executionState.value;
   const newState = { ...currentState, ...updates };
   this.executionState.next(newState);
 }


 /**
  * Get current execution state
  */
 getCurrentState(): SequentialRegenerationState {
   return this.executionState.value;
 }


 /**
  * Reset service state - ENHANCED for complete cleanup
  */
 reset(): void {
   this.logger.info('🔄 Starting comprehensive sequential regeneration service reset');

   // Update execution state
   this.updateExecutionState({
     isExecuting: false,
     introCompleted: false,
     regenerationCompleted: false,
     sseConnected: false,
     hasErrors: false,
     startTime: 0,
     currentPhase: 'intro'
   });

   // CRITICAL FIX: Clear active sessions
   this.activeRegenerationSessions.clear();

   // CRITICAL FIX: Clear any active SSE streams
   this.activeSSEStreams.clear();

   // CRITICAL FIX: Reset stepper state to ensure it's not stuck in regeneration mode
   this.stepperStateService.setRegenerationActive(false);

   // CRITICAL FIX: Clear any pending timeouts or intervals
   this.performFailureCleanup();

   this.logger.info('✅ Sequential Regeneration Service reset completed with comprehensive cleanup');
 }


 /**
  * Check if a message should be suppressed to prevent unwanted deployment notifications
  * ENHANCEMENT: Filters out unwanted deployment success messages during regeneration
  */
 private shouldSuppressMessage(eventData: any): boolean {
   // Suppress messages that contain unwanted deployment success text
   const unwantedPhrases = [
     'Your application has been successfully built and deployed',
     'successfully built and deployed! You can view it live here',
     'application has been successfully built and deployed',
     'built and deployed! You can view it live',
     'Your application has been successfully built and deployed! You can view it live here',
     'Your application has been successfully built and deployed! You can view it on the preview tab',
     'application has been successfully deployed',
     'successfully deployed! You can view it live',
     'successfully deployed! You can view it on the preview tab',
     'deployment completed successfully',
     'view it live here',
     'view it on the preview tab'
   ];


   // Check all possible fields that might contain the unwanted message
   const fieldsToCheck = [
     eventData.log,
     eventData.progress_description,
     eventData.message,
     eventData.description,
     eventData.text,
     eventData.content,
     eventData.notification,
     eventData.alert
   ];


   for (const field of fieldsToCheck) {
     if (field && typeof field === 'string') {
       const fieldLower = field.toLowerCase();
       if (unwantedPhrases.some(phrase => fieldLower.includes(phrase.toLowerCase()))) {
         this.logger.info('🚫 Suppressing unwanted deployment message in field:', { field, phrase: unwantedPhrases.find(p => fieldLower.includes(p.toLowerCase())) });
         return true;
       }
     }
   }


   // Also check if the entire event data contains the unwanted message when stringified
   try {
     const eventDataString = JSON.stringify(eventData).toLowerCase();
     if (unwantedPhrases.some(phrase => eventDataString.includes(phrase.toLowerCase()))) {
       this.logger.info('🚫 Suppressing unwanted deployment message in event data:', eventData);
       return true;
     }
   } catch (error) {
     // Ignore JSON stringify errors
   }


   return false;
 }


 /**
  * ENHANCED: Handle initial code generation completion and SSE connection closure
  *
  * Automatically closes SSE connection when initial-code-gen is complete and final.
  * This implements the requirement to detect completion conditions and close connections
  * to prevent unnecessary resource usage.
  *
  * Detection Conditions:
  * - event: "initial-code-gen"
  * - status: "COMPLETED"
  * - progress: "DEPLOY"
  * - is_final: true (or isFinal: true)
  *
  * Actions Performed:
  * - Closes enhanced SSE service connection
  * - Disconnects base SSE service if connected
  * - Clears initial code generation streams from cache
  * - Updates execution state to reflect completion
  * - Comprehensive error handling with graceful degradation
  *
  * Integration:
  * - Works with existing event filtering and validation
  * - Preserves regeneration streams while cleaning up initial generation
  * - Uses Angular 19+ patterns with proper cleanup
  */
 private handleInitialCodeGenerationCompletion(event: SSEEvent, eventData: any): void {
   // Only process initial-code-gen events
   if (event.event !== 'initial-code-gen') {
     return;
   }

   // Check for completion conditions
   const isCompleted = eventData.status === 'COMPLETED';
   const isDeployPhase = eventData.progress === 'DEPLOY';
   const isFinal = eventData.is_final === true || eventData.isFinal === true;

   this.logger.debug('🔍 Checking initial code generation completion conditions:', {
     eventType: event.event,
     eventId: event.id,
     status: eventData.status,
     progress: eventData.progress,
     isFinal: eventData.is_final || eventData.isFinal,
     isCompleted,
     isDeployPhase,
     shouldClose: isCompleted && isDeployPhase && isFinal
   });

   // If all conditions are met, close the SSE connection
   if (isCompleted && isDeployPhase && isFinal) {
     this.logger.info('🏁 Initial code generation completed - closing SSE connection', {
       eventType: event.event,
       eventId: event.id,
       status: eventData.status,
       progress: eventData.progress,
       isFinal: eventData.is_final || eventData.isFinal,
       timestamp: new Date().toISOString()
     });

     // Close the SSE connection for initial code generation
     this.closeInitialCodeGenerationConnection(event, eventData);
   }
 }

 /**
  * ENHANCED: Track regeneration subscription for proper cleanup
  * This method should be called when consumers subscribe to regeneration observables
  */
 trackRegenerationSubscription(projectId: string, jobId: string, subscription: Subscription): void {
   const connectionKey = `${projectId}-${jobId}`;

   this.logger.info('📝 Tracking regeneration subscription:', {
     connectionKey,
     subscriptionClosed: subscription.closed,
     timestamp: new Date().toISOString()
   });

   // Track subscription in enhanced SSE service
   this.enhancedSSEService.trackSubscription(connectionKey, subscription);

   // Update UI state to reflect active regeneration
   this.enhancedSSEService.updateConnectionUIState(connectionKey, {
     isLoading: true,
     progressDescription: 'Regenerating...'
   });
 }

 /**
  * ENHANCED: Close SSE connection for completed initial code generation
  * Uses Angular 19+ patterns with proper cleanup and error handling
  */
 private closeInitialCodeGenerationConnection(event: SSEEvent, _eventData: any): void {
   try {
     this.logger.info('🔌 Closing initial code generation SSE connection', {
       eventId: event.id,
       reason: 'Initial code generation completed and marked as final'
     });

     // Close enhanced SSE service connection
     if (this.enhancedSSEService.isMonitoring()) {
       this.logger.info('🧹 Stopping enhanced SSE service monitoring for initial code generation');
       this.enhancedSSEService.stopMonitoring();
     }

     // Close base SSE service connection if connected
     if (this.sseService.isConnected()) {
       this.logger.info('🧹 Disconnecting base SSE service for initial code generation');
       this.sseService.disconnect();
     }

     // Clear any cached SSE streams for initial code generation
     this.clearInitialCodeGenerationStreams();

     // Update execution state to reflect completion
     this.updateExecutionState({
       sseConnected: false,
       currentPhase: 'completed'
     });

     this.logger.info('✅ Initial code generation SSE connection closed successfully', {
       enhancedSSEClosed: !this.enhancedSSEService.isMonitoring(),
       baseSSEClosed: !this.sseService.isConnected(),
       executionPhase: 'completed',
       timestamp: new Date().toISOString()
     });

   } catch (error) {
     this.logger.error('❌ Error closing initial code generation SSE connection:', {
       error: error instanceof Error ? error.message : String(error),
       eventId: event.id,
       fallbackAction: 'Connection may remain open'
     });

     // Attempt graceful degradation - try to at least disconnect base service
     try {
       if (this.sseService.isConnected()) {
         this.sseService.disconnect();
         this.logger.info('✅ Fallback: Base SSE service disconnected');
       }
     } catch (fallbackError) {
       this.logger.error('❌ Fallback disconnect also failed:', fallbackError);
     }
   }
 }

 /**
  * ENHANCED: Clear SSE streams specifically for initial code generation
  * Preserves regeneration streams while cleaning up initial generation streams
  */
 private clearInitialCodeGenerationStreams(): void {
   this.logger.info('🧹 Clearing initial code generation SSE streams');

   // Clear streams that are specifically for initial code generation
   // Note: This preserves any active regeneration streams
   const streamsToRemove: string[] = [];

   this.activeSSEStreams.forEach((_stream, key) => {
     // Identify initial code generation streams by key pattern or context
     // This is a conservative approach to avoid affecting regeneration streams
     if (key.includes('initial') || key.includes('first-gen')) {
       streamsToRemove.push(key);
     }
   });

   streamsToRemove.forEach(key => {
     this.activeSSEStreams.delete(key);
     this.logger.debug('🗑️ Removed initial code generation stream:', key);
   });

   this.logger.info('✅ Initial code generation streams cleared', {
     removedStreams: streamsToRemove.length,
     remainingStreams: this.activeSSEStreams.size
   });
 }

 /**
  * ENHANCED: Test initial code generation completion logic
  * For debugging and verification purposes
  */
 public testInitialCodeGenerationCompletion(): void {
   this.logger.info('🧪 Testing initial code generation completion logic');

   // Create mock event data for testing
   const mockEvent: SSEEvent = {
     id: `test-${Date.now()}`,
     event: 'initial-code-gen',
     data: JSON.stringify({
       status: 'COMPLETED',
       progress: 'DEPLOY',
       is_final: true,
       message: 'Test completion event'
     })
   };

   const mockEventData = JSON.parse(mockEvent.data);

   this.logger.info('🧪 Mock event created:', {
     eventType: mockEvent.event,
     eventId: mockEvent.id,
     status: mockEventData.status,
     progress: mockEventData.progress,
     isFinal: mockEventData.is_final
   });

   // Test the completion handling
   this.handleInitialCodeGenerationCompletion(mockEvent, mockEventData);

   this.logger.info('🧪 Initial code generation completion test completed');
 }

 /**
  * ENHANCED: Validate SSE event for regeneration processing
  * Checks event type, ID, and data structure
  */
 private validateSSEEvent(event: SSEEvent, expectedEventType: string): boolean {
   // Check if event type matches expected type
   if (event.event !== expectedEventType) {
     this.logger.debug('⏭️ Event type mismatch:', {
       received: event.event,
       expected: expectedEventType,
       reason: 'Event type does not match expected type for regeneration'
     });
     return false;
   }

   // Check if event has valid ID
   if (!event.id || event.id.trim() === '') {
     this.logger.warn('⚠️ Event missing valid ID:', {
       eventType: event.event,
       hasId: !!event.id,
       reason: 'Events without IDs cannot be properly tracked'
     });
     // Allow processing but log warning
   }

   // Check if event has data
   if (!event.data || event.data.trim() === '') {
     this.logger.warn('⚠️ Event missing data:', {
       eventType: event.event,
       eventId: event.id,
       hasData: !!event.data,
       reason: 'Events without data cannot be processed'
     });
     return false;
   }

   // Try to parse data to ensure it's valid JSON
   try {
     JSON.parse(event.data);
   } catch (error) {
     this.logger.error('❌ Event data is not valid JSON:', {
       eventType: event.event,
       eventId: event.id,
       error: error instanceof Error ? error.message : String(error),
       reason: 'Cannot process event with malformed data'
     });
     return false;
   }

   return true;
 }

 /**
  * ENHANCED: Check if SSE event is duplicate based on ID with session context
  */
 private isDuplicateEvent(event: SSEEvent): boolean {
   if (!event.id || !this.currentRegenerationSessionId) {
     return false; // No ID or session to compare, process the event
   }

   // Get session-specific processed events
   const sessionEvents = this.processedEventIds.get(this.currentRegenerationSessionId);
   if (!sessionEvents) {
     return false; // No events processed for this session yet
   }

   // Create composite key for better duplicate detection
   const eventKey = `${event.event || 'update'}-${event.id}`;

   if (sessionEvents.has(eventKey)) {
     this.logger.debug('🔄 Duplicate event detected for session:', {
       sessionId: this.currentRegenerationSessionId,
       eventKey,
       id: event.id,
       eventType: event.event
     });
     return true;
   }

   // Mark as processed for this session
   sessionEvents.add(eventKey);
   this.lastProcessedEventId = eventKey;

   this.logger.debug('✅ New event processed for session:', {
     sessionId: this.currentRegenerationSessionId,
     eventKey,
     id: event.id,
     eventType: event.event,
     totalProcessedInSession: sessionEvents.size
   });

   return false;
 }


 /**
  * ENHANCED: Perform comprehensive cleanup on regeneration failure
  * Clears all state and streams to prevent interference with future regenerations
  */
 private performFailureCleanup(): void {
   this.logger.info('🧹 Performing failure cleanup for regeneration');

   // Clear processed event IDs
   this.processedEventIds.clear();
   this.lastProcessedEventId = null;

   // Clear active SSE streams
   this.activeSSEStreams.clear();

   // Reset execution state
   this.updateExecutionState({
     isExecuting: false,
     introCompleted: false,
     regenerationCompleted: false,
     sseConnected: false,
     hasErrors: true,
     startTime: 0,
     currentPhase: 'error'
   });

   this.logger.info('✅ Failure cleanup completed', {
     processedEventIds: this.processedEventIds.size,
     activeStreams: this.activeSSEStreams.size,
     executionState: 'error-cleaned'
   });
 }

 /**
  * ENHANCED: Reset event tracking with session-aware cleanup
  * ENHANCED: Also cleanup active SSE streams and reset execution state
  */
 private resetEventTracking(): void {
   // Clear all session-specific processed event IDs
   this.processedEventIds.clear();
   this.lastProcessedEventId = null;

   // ENHANCED: Clear active SSE streams to prevent interference
   this.activeSSEStreams.clear();

   // Clear active regeneration sessions (keep only current if exists)
   if (this.currentRegenerationSessionId) {
     const currentSession = this.activeRegenerationSessions.get(this.currentRegenerationSessionId);
     this.activeRegenerationSessions.clear();
     if (currentSession) {
       this.activeRegenerationSessions.set(this.currentRegenerationSessionId, currentSession);
     }
   } else {
     this.activeRegenerationSessions.clear();
   }

   // ENHANCED: Reset execution state for clean start
   this.updateExecutionState({
     isExecuting: false,
     introCompleted: false,
     regenerationCompleted: false,
     sseConnected: false,
     hasErrors: false,
     startTime: 0,
     currentPhase: 'intro'
   });

   this.logger.info('🧹 Reset SSE event tracking and streams for new regeneration', {
     processedEventIds: this.processedEventIds.size,
     activeStreams: this.activeSSEStreams.size,
     activeSessions: this.activeRegenerationSessions.size,
     currentSessionId: this.currentRegenerationSessionId,
     executionState: 'reset'
   });
 }

 /**
  * Cache event ID for regeneration checkpointing
  * @param eventId The event ID to cache
  * @param projectId Project ID for session key
  * @param jobId Job ID for session key
  */
 private cacheEventIdForCheckpointing(eventId: string, projectId: string, jobId: string): void {
   const sessionKey = `${projectId}-${jobId}`;

   this.logger.info('💾 Caching event ID for regeneration checkpointing:', {
     eventId,
     sessionKey,
     projectId,
     jobId,
     timestamp: new Date().toISOString()
   });

   // Cache the event ID in the SSE service for future regeneration sessions
   try {
     this.sseService.cacheEventId(eventId, sessionKey);
     this.logger.info('✅ Event ID successfully cached for future regeneration sessions');
   } catch (error) {
     this.logger.warn('⚠️ Failed to cache event ID:', error);
   }
 }
}
