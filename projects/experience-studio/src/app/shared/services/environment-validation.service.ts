import { Injectable, inject } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ToastService } from './toast.service';
import { createLogger } from '../utils/logger';

/**
 * Environment validation result interface
 */
export interface EnvironmentValidationResult {
  isValid: boolean;
  error?: string;
  apiUrl?: string;
}

/**
 * Environment validation error class
 */
export class EnvironmentValidationError extends Error {
  constructor(
    message: string,
    public readonly validationResult: EnvironmentValidationResult
  ) {
    super(message);
    this.name = 'EnvironmentValidationError';
  }
}

/**
 * Environment Validation Service
 * Validates environment.apiUrl before HTTP requests are made
 * Uses Angular 19+ patterns with inject() and proper error handling
 */
@Injectable({
  providedIn: 'root'
})

export class EnvironmentValidationService {
  private readonly toastService = inject(ToastService);
  private readonly logger = createLogger('EnvironmentValidationService');

  // Cache validation result to avoid repeated checks
  private validationCache: EnvironmentValidationResult | null = null;
  private readonly USER_FRIENDLY_ERROR_MESSAGE = 'No server URL is available. Please check your environment configuration and retry again.';

  // Flag to ensure only one toast per invalidation event
  private hasShownToastForCurrentInvalidation = false;

  constructor() {
    this.logger.info('🔧 Environment Validation Service initialized');
  }

  /**
   * Validate environment API URL
   * Returns cached result if already validated
   */
  validateEnvironmentUrl(): EnvironmentValidationResult {
    // Return cached result if available
    if (this.validationCache) {
      // Reset toast flag if environment is now valid
      if (this.validationCache.isValid) {
        this.hasShownToastForCurrentInvalidation = false;
      }
      return this.validationCache;
    }

    const result = this.performValidation();

    // Reset toast flag if environment is now valid
    if (result.isValid) {
      this.hasShownToastForCurrentInvalidation = false;
    }

    // Cache the result for subsequent calls
    this.validationCache = result;

    return result;
  }

  /**
   * Validate environment URL and throw error if invalid
   * Use this method in interceptors to prevent invalid requests
   */
  validateOrThrow(): void {
    const result = this.validateEnvironmentUrl();

    if (!result.isValid) {
      this.logger.error('❌ Environment validation failed:', result.error);

      // Only show toast if not already shown for this invalidation
      if (!this.hasShownToastForCurrentInvalidation) {
        this.toastService.error(this.USER_FRIENDLY_ERROR_MESSAGE);
        this.hasShownToastForCurrentInvalidation = true;
      }

      // Throw validation error to prevent HTTP request
      throw new EnvironmentValidationError(
        result.error || 'Environment validation failed',
        result
      );
    }
  }

  /**
   * Validate environment URL and return observable error if invalid
   * Use this method in services for RxJS error handling
   */
  validateOrReturnError<T>(): Observable<T> | null {
    const result = this.validateEnvironmentUrl();

    if (!result.isValid) {
      this.logger.error('❌ Environment validation failed:', result.error);

      // Only show toast if not already shown for this invalidation
      if (!this.hasShownToastForCurrentInvalidation) {
        this.toastService.error(this.USER_FRIENDLY_ERROR_MESSAGE);
        this.hasShownToastForCurrentInvalidation = true;
      }

      // Return observable error
      return throwError(() => new EnvironmentValidationError(
        result.error || 'Environment validation failed',
        result
      ));
    }

    return null; // Validation passed
  }

  /**
   * Get current API URL if valid
   */
  getValidApiUrl(): string | null {
    const result = this.validateEnvironmentUrl();
    return result.isValid ? result.apiUrl! : null;
  }

  /**
   * Reset validation cache (useful for testing or environment changes)
   */
  resetValidationCache(): void {
    this.validationCache = null;
    this.hasShownToastForCurrentInvalidation = false;
    this.logger.info('🔄 Environment validation cache reset');
  }

  /**
   * Perform the actual validation logic
   */
  private performValidation(): EnvironmentValidationResult {
    const apiUrl = environment.apiUrl;

    // Check if apiUrl exists and is not null/undefined
    if (!apiUrl) {
      return {
        isValid: false,
        error: 'The server URL is not defined in the environment configuration.'
      };
    }

    // Check if apiUrl is not empty or whitespace-only
    if (typeof apiUrl !== 'string' || apiUrl.trim().length === 0) {
      return {
        isValid: false,
        error: 'The server URL in the environment configuration is empty or contains only whitespace.'
      };
    }

    // Basic URL format validation
    if (!this.isValidUrlFormat(apiUrl.trim())) {
      return {
        isValid: false,
        error: 'The server URL specified in the environment configuration is not in a valid URL format.'
      };
    }

    // Validation passed
    this.logger.info('✅ Environment validation successful:', { apiUrl: apiUrl.trim() });

    return {
      isValid: true,
      apiUrl: apiUrl.trim()
    };
  }

  /**
   * Basic URL format validation
   */
  private isValidUrlFormat(url: string): boolean {
    try {
      // Use URL constructor for basic validation
      const urlObject = new URL(url);

      // Check for required protocol (http or https)
      if (!['http:', 'https:'].includes(urlObject.protocol)) {
        return false;
      }

      // Check for valid hostname
      if (!urlObject.hostname || urlObject.hostname.length === 0) {
        return false;
      }

      return true;
    } catch (error) {
      // URL constructor throws for invalid URLs
      return false;
    }
  }

  /**
   * Get validation status for debugging
   */
  getValidationStatus(): {
    hasCache: boolean;
    cacheResult: EnvironmentValidationResult | null;
    currentEnvironment: {
      apiUrl: any;
      production: boolean;
    };
  } {
    return {
      hasCache: !!this.validationCache,
      cacheResult: this.validationCache,
      currentEnvironment: {
        apiUrl: environment.apiUrl,
        production: environment.production
      }
    };
  }
}
