# =============================================================================
# Docker Compose Configuration for Experience Studio Application
# =============================================================================
# 
# This file defines the services for running the Experience Studio application
# in both production and development environments.
# =============================================================================

version: '3.8'

# =============================================================================
# Services Configuration
# =============================================================================
services:
  # =====================================================================
  # Production Service
  # =====================================================================
  experience-studio:
    build:
      context: ../..
      dockerfile: projects/experience-studio/Dockerfile
      target: production
    container_name: experience-studio-app
    ports:
      - "8081:8080"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - experience-studio-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.experience-studio.rule=Host(`experience-studio.localhost`)"
      - "traefik.http.services.experience-studio.loadbalancer.server.port=8080"

  # =====================================================================
  # Development Service (Optional)
  # =====================================================================
  experience-studio-dev:
    build:
      context: ../..
      dockerfile: projects/experience-studio/Dockerfile.dev
      target: development
    container_name: experience-studio-dev
    ports:
      - "4201:4201"
    environment:
      - NODE_ENV=development
    volumes:
      - ../../:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - experience-studio-network
    profiles:
      - dev

# =============================================================================
# Networks Configuration
# =============================================================================
networks:
  experience-studio-network:
    driver: bridge 