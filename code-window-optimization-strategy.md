  
**Target**: Set up all service classes and dependency injection
1. Create service directory structure:
   ```
   services/
   ├── code-window-sse.service.ts
   ├── code-window-file.service.ts
   ├── code-window-ui-design.service.ts
   ├── code-window-regeneration.service.ts
   ├── code-window-artifact.service.ts
   ├── code-window-preview.service.ts
   ├── code-window-state.service.ts
   ├── code-window-utils.service.ts
   ├── code-window-chat.service.ts
   └── code-window-design-token.service.ts
   ```
2. Define all service interfaces with proper TypeScript types
3. Create injectable service classes with empty implementations
4. Set up dependency injection in component constructor
5. Update component imports and provider declarations

### Step 2: Extract SSE Management (Week 1)
**Target**: Reduce component by ~800 lines
1. Move SSE-related methods to `CodeWindowSSEService`
2. Update component to use service methods via dependency injection
3. Preserve all existing SSE functionality and event handling
4. Test SSE connectivity, reconnection, and error handling
5. Verify regeneration progress updates work correctly
6. Ensure no breaking changes to parent component communication

### Step 3: Extract File Operations (Week 2)
**Target**: Reduce component by ~600 lines
1. Move file-related methods to `CodeWindowFileService`
2. Update component file handling logic to use service
3. Preserve all file export/download functionality
4. Test VSCode export integration thoroughly
5. Verify ZIP file generation and download works
6. Test file tree persistence and state management

### Step 4: Extract UI Design Workflow (Week 2-3)
**Target**: Reduce component by ~1,200 lines
1. Move UI design methods to `CodeWindowUIDesignService`
2. Update component UI design logic to use service
3. Preserve all UI design generation/regeneration workflows
4. Test node selection, editing, and multi-page alignment
5. Verify canvas operations and viewport management
6. Ensure design token integration remains functional

### Step 5: Extract Business Logic Services (Week 3)
**Target**: Reduce component by ~1,300 lines
1. Extract regeneration service with all regeneration workflows
2. Extract artifact service with processing and validation
3. Extract preview service with deployment management
4. Update component to use all three services
5. Test complete regeneration pipeline end-to-end
6. Verify artifact processing and preview generation

### Step 6: Extract Supporting Services (Week 4)
**Target**: Reduce component by ~900 lines
1. Extract state management service with centralized state
2. Extract utility functions service with helper methods
3. Extract chat service with message handling
4. Extract design token service with token management
5. Update component to use all supporting services
6. Test complete integration of all services

### Step 7: Final Optimization and Testing (Week 4)
**Target**: Achieve final line count under 3,000 lines
1. Remove any remaining duplicate code
2. Optimize service-to-service communication
3. Implement comprehensive unit tests for all services
4. Perform integration testing of complete workflows
5. Conduct performance benchmarking
6. Document all service interfaces and usage patterns

## Safety Measures

### Template Binding Preservation
**Critical Requirement**: Zero breaking changes to component's public interface
- **Public Methods**: All template-bound methods remain public in component
- **Public Properties**: All template-bound properties remain public in component
- **Event Handlers**: All template event handlers preserved exactly as-is
- **Input/Output Properties**: All @Input and @Output decorators unchanged
- **Component API**: Parent component communication remains identical

**Template Analysis Results**:
Based on template examination, these methods MUST remain public:
- `navigateToHome()` - Header navigation
- `toggleLeftPanel()` - Panel control
- `getPromptBarPlaceholder()` - Chat window binding
- `shouldShowStepper()` - Progress display
- `getPromptBarEnabledState()` - Input state
- `shouldShowUIDesignLoadingIndicator()` - Loading state
- `shouldShowCodeGenerationLoadingIndicator()` - Loading state
- `handleIconClick()` - Icon interactions
- `handleUIDesignPromptSubmission()` - Form submission
- `handleRegenerationPayload()` - Regeneration workflow
- `handleUserMessageData()` - Message handling
- `onRetryClick()` - Error recovery
- `toggleHistoryView()` - History navigation
- All async pipe observables and their getters

### Testing Strategy
**Phase-by-Phase Validation**:
1. **Unit Tests**: Each extracted service tested in isolation
2. **Integration Tests**: Component-service interactions validated
3. **Template Tests**: All template bindings verified functional
4. **E2E Tests**: Complete user workflows tested end-to-end
5. **Performance Tests**: Bundle size and runtime performance measured
6. **Regression Tests**: Existing functionality verified unchanged

**Test Coverage Requirements**:
- 100% coverage of extracted service methods
- 100% coverage of remaining component public methods
- All template bindings tested with mock data
- All user interaction flows tested

### Rollback Plan
**Git Strategy**:
- Feature branch for each service extraction phase
- Atomic commits for each service extraction
- Ability to cherry-pick or revert individual extractions
- Comprehensive backup of original 11,679-line component

**Rollback Triggers**:
- Any functionality regression detected
- Performance degradation beyond acceptable limits
- Template binding failures
- Integration test failures
- User acceptance test failures

## Expected Benefits

### Performance Improvements
- **Bundle Size**: 75% reduction in component size (11,679 → 3,000 lines)
- **Memory Usage**: Reduced through better garbage collection and service lifecycle management
- **Load Time**: Faster initial component loading due to smaller bundle
- **Tree Shaking**: Better Angular optimization with focused service imports
- **Change Detection**: Improved OnPush performance with isolated state management
- **Lazy Loading**: Services can be lazy-loaded when needed

### Maintainability Improvements
- **Single Responsibility**: Each service handles one specific business domain
- **Testability**: Isolated services much easier to unit test and mock
- **Debugging**: Clearer separation of concerns makes issue tracking easier
- **Code Reuse**: Services can be reused by other components in the application
- **Dependency Management**: Clear service dependencies vs monolithic coupling
- **Code Reviews**: Smaller, focused changes easier to review

### Development Experience
- **Readability**: Smaller, focused code files (300-800 lines vs 11,679 lines)
- **Collaboration**: Multiple developers can work on different services simultaneously
- **Documentation**: Clearer service boundaries and well-defined responsibilities
- **Refactoring**: Easier to modify individual features without affecting others
- **IDE Performance**: Better IntelliSense and code navigation in smaller files
- **Git History**: Cleaner commit history with focused changes per service

### Architecture Improvements
- **Scalability**: New features can be added as new services
- **Modularity**: Services can be moved to feature modules if needed
- **Dependency Injection**: Proper Angular DI patterns throughout
- **Observable Patterns**: Consistent reactive programming across services
- **Error Handling**: Centralized error handling per business domain
- **State Management**: Clear state ownership and data flow

## Risk Mitigation

### Technical Risks
- **Circular Dependencies**: Careful service design to avoid cycles
- **Performance Overhead**: Minimize service-to-service communication
- **State Synchronization**: Proper observable patterns for state sharing

### Business Risks
- **Feature Regression**: Comprehensive testing at each phase
- **User Experience**: No changes to existing functionality
- **Timeline**: Phased approach allows for adjustments

## Success Metrics

### Quantitative Metrics
- Component size reduced from 11,679 to under 3,000 lines
- Number of services created: 8-10 specialized services
- Test coverage maintained at 100%
- No increase in bundle size due to service overhead

### Qualitative Metrics
- Improved code maintainability score
- Reduced complexity metrics
- Better separation of concerns
- Enhanced developer experience

## Detailed Service Extraction Mapping

### Critical Line Number Mappings
Based on component analysis, here are the specific line ranges for extraction:

**SSE Management Service** (800 lines total):
- Lines 4069-4351: Core SSE subscription methods
- Lines 7069-7170: SSE event filtering and synchronization
- Lines 10253-10273: SSE fallback and error handling

**File Operations Service** (600 lines total):
- Lines 8961-9316: File tree operations and code processing
- Lines 9430-9554: VSCode export and ZIP generation

**UI Design Workflow Service** (1,200 lines total):
- Lines 1864-2100: UI design generation initiation
- Lines 2372-2877: Loading node management and parallel API calls
- Lines 3666-3924: UI design response processing and error handling

**Code Regeneration Service** (1,000 lines total):
- Lines 3666-4351: Regeneration payload handling and workflow
- Lines 8664-8961: Sequential regeneration and progress tracking

**Artifact Management Service** (500 lines total):
- Lines 839-1308: Artifact processing and validation
- Lines 7849-8048: Artifact updates and merging

**Preview/Deployment Service** (400 lines total):
- Lines 5055-5615: Preview URL management and validation
- Lines 8718-8961: Deployment status and error handling

**State Management Service** (300 lines total):
- Lines 1308-1864: Component state initialization
- Lines 2100-2372: Tab state and loading state management
- Lines 4351-4669: State synchronization and persistence

**Utility Functions Service** (200 lines total):
- Lines 4669-5055: Helper functions and formatters
- Lines 7170-7849: Validation and parsing utilities

**Chat/Messaging Service** (400 lines total):
- Lines 5615-6055: Chat message processing
- Lines 6355-6669: Message queue and retry handling

**Design Token Service** (300 lines total):
- Lines 6669-7069: Design token processing and application

### Component Structure After Optimization

**Final Component Structure** (~2,800 lines):
```typescript
@Component({...})
export class CodeWindowComponent implements OnInit, OnDestroy {
  // Public properties for template binding (~200 lines)
  // Constructor with service injection (~50 lines)
  // Lifecycle methods (~100 lines)
  // Public event handlers for template (~300 lines)
  // Public getters for template bindings (~150 lines)
  // Service coordination methods (~200 lines)
  // Component-specific UI logic (~800 lines)
  // Cleanup and utility methods (~100 lines)
  // Observable subscriptions and cleanup (~900 lines)
}
```

## Conclusion

This comprehensive optimization strategy provides a systematic, risk-minimized approach to reducing the code-window component from 11,679 lines to under 3,000 lines while maintaining 100% functionality.

**Key Success Factors**:
- **Microservice Architecture**: Each service handles a single business domain
- **Template Binding Preservation**: All public APIs remain unchanged
- **Phased Implementation**: Risk is minimized through incremental extraction
- **Comprehensive Testing**: Every phase includes thorough validation
- **Angular Best Practices**: Modern patterns and dependency injection throughout

**Expected Outcome**:
- **75% Line Reduction**: From 11,679 to under 3,000 lines
- **10 Specialized Services**: Each focused on a specific business domain
- **Zero Functionality Loss**: All existing features preserved exactly
- **Improved Performance**: Better bundle size, memory usage, and load times
- **Enhanced Maintainability**: Easier debugging, testing, and feature development

The phased implementation plan minimizes risk and allows for continuous validation of functionality throughout the optimization process, ensuring a successful transformation of this critical component.
