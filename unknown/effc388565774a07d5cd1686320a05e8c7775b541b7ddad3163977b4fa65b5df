<div class="org-config-wrapper">
  <div class="org-config-container">
    <h2>Please select the Realm configuration</h2>
    <form [formGroup]="configForm">
      <div class="dropdown-row">
        <div class="dropdown-group">
          <ava-dropdown
            dropdownTitle="Select Organization"
            [options]="orgOptions"
            [selectedValue]="configForm.get('org')?.value"
            (selectionChange)="onOrgSelect($event)"
            [search]="true"
            [enableSearch]="true">
          </ava-dropdown>
        </div>
        <div class="dropdown-group">
          <ava-dropdown
            dropdownTitle="Select Domain"
            [options]="domainOptions"
            [selectedValue]="configForm.get('domain')?.value"
            (selectionChange)="onDomainSelect($event)"
            [search]="true"
            [enableSearch]="true"
            [disabled]="!configForm.get('org')?.value">
          </ava-dropdown>
        </div>
        <div class="dropdown-group">
          <ava-dropdown
            dropdownTitle="Select Project"
            [options]="projectOptions"
            [selectedValue]="configForm.get('project')?.value"
            (selectionChange)="onProjectSelect($event)"
            [search]="true"
            [enableSearch]="true"
            [disabled]="!configForm.get('domain')?.value">
          </ava-dropdown>
        </div>
        <div class="dropdown-group">
          <ava-dropdown
            dropdownTitle="Select Team"
            [options]="teamOptions"
            [selectedValue]="configForm.get('team')?.value"
            (selectionChange)="onTeamSelect($event)"
            [search]="true"
            [enableSearch]="true"
            [disabled]="!configForm.get('project')?.value">
          </ava-dropdown>
        </div>
      </div>
      <div class="apply-filters-btn-row">
        <ava-button 
          label="Apply Filters"
          variant="primary" 
          size="medium" 
          (userClick)="onSaveConfig()" 
          [disabled]="!isFormValid()">
        </ava-button>
      </div>
    </form>
  </div>
</div>
